{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "24+D+0D5v+3pdDaQW7b/IFJnIg+dbWioNV6s+DMSzFM=", "__NEXT_PREVIEW_MODE_ID": "60c5e5d6103024ca89d599e3a7b8542c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9899151d20a63833469507857470ec09b797d4e19c037b04227a3a3698d0f240", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "877833251f38a75f8956c20f46426ae238d3a3289e596093b9892211efae48fd"}}}, "functions": {}, "sortedMiddleware": ["/"]}