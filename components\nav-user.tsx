"use client";

import {
  RiMore2Line,
  RiDashboardLine,
  RiUserLine,
  RiSettings3Line,
  RiFileTextLine,
  RiLogoutCircleLine,
} from "@remixicon/react";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

export function NavUser({
  user,
}: {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
}) {
  const { isMobile } = useSidebar();
  const { signOut } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/sign-in');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="in-data-[state=expanded]:size-6 transition-[width,height] duration-200 ease-in-out">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {user.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight ms-1">
                <span className="truncate font-medium">{user.name}</span>
                <span className="truncate text-xs text-muted-foreground">{user.email}</span>
              </div>
              <div className="size-8 rounded-lg flex items-center justify-center bg-sidebar-accent/50 in-[[data-slot=dropdown-menu-trigger]:hover]:bg-transparent">
                <RiMore2Line className="size-5 opacity-40" size={20} />
              </div>
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuItem className="gap-3 px-1" onClick={() => router.push('/dashboard')}>
              <RiDashboardLine
                size={20}
                className="text-muted-foreground/70"
                aria-hidden="true"
              />
              <span>Dashboard</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="gap-3 px-1" onClick={() => router.push('/dashboard/perfil')}>
              <RiUserLine
                size={20}
                className="text-muted-foreground/70"
                aria-hidden="true"
              />
              <span>Meu Perfil</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="gap-3 px-1" onClick={() => router.push('/dashboard/configuracoes')}>
              <RiSettings3Line
                size={20}
                className="text-muted-foreground/70"
                aria-hidden="true"
              />
              <span>Configurações</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="gap-3 px-1" onClick={() => router.push('/dashboard/relatorios')}>
              <RiFileTextLine
                size={20}
                className="text-muted-foreground/70"
                aria-hidden="true"
              />
              <span>Relatórios</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-3 px-1 text-red-600 focus:text-red-600 focus:bg-red-50 dark:focus:bg-red-950" onClick={handleLogout}>
              <RiLogoutCircleLine
                size={20}
                className="text-red-600"
                aria-hidden="true"
              />
              <span>Sair</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
