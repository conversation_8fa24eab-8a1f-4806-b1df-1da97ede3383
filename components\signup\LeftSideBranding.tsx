'use client'

import React from 'react';
import { LanguageSelector } from './LanguageSelector';
import { VerticalCutReveal } from '@/components/ui/vertical-cut-reveal';

export const LeftSideBranding: React.FC = () => {
  return (
    <aside className="box-border w-[551px] min-h-screen relative shrink-0 bg-gradient-to-br from-[#ffd700] to-[#fa12e3] left-[background] max-md:w-full max-md:min-h-[300px] max-md:order-2">
      <div className="absolute left-16 top-16">
        <img 
          src="/logo-afc.svg" 
          alt="AFace Company Logo" 
          width="204" 
          height="96"
          className="object-contain"
        />
      </div>

      {/* Texto animado central */}
      <div className="absolute inset-0 flex items-center justify-center p-8">
        <div className="w-full max-w-lg">
          <div className="w-full h-full text-xl sm:text-2xl md:text-3xl lg:text-4xl flex flex-col items-start justify-center font-semibold text-white tracking-wide  leading-tight">
            <VerticalCutReveal
              splitBy="characters"
              staggerDuration={0.025}
              staggerFrom="first"
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 21,
              }}
            >
              {`E AÍ 👋, VAMOS?`}
            </VerticalCutReveal>
            
            <VerticalCutReveal
              splitBy="characters"
              staggerDuration={0.025}
              staggerFrom="last"
              reverse={true}
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 21,
                delay: 0.5,
              }}
            >
              {`🌟 QUE TAL 💡 CRIAR`}
            </VerticalCutReveal>
            
            <VerticalCutReveal
              splitBy="characters"
              staggerDuration={0.025}
              staggerFrom="center"
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 21,
                delay: 1.1,
              }}
            >
              {`ALGO INCRÍVEL 🚀`}
            </VerticalCutReveal>

            <VerticalCutReveal
              splitBy="characters"
              staggerDuration={0.02}
              staggerFrom="first"
              transition={{
                type: "spring",
                stiffness: 190,
                damping: 20,
                delay: 1.8,
              }}
            >
              {`✨ JUNTOS HOJE?`}
            </VerticalCutReveal>
          </div>
        </div>
      </div>

      <LanguageSelector />
    </aside>
  );
};
