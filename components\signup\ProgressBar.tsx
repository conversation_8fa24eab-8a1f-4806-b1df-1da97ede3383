import React from 'react';

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({ currentStep, totalSteps }) => {
  return (
    <div className="box-border flex w-[calc(100%_-_128px)] h-1 justify-center items-start gap-2 mb-7 max-md:w-[calc(100%_-_64px)] max-sm:w-[calc(100%_-_32px)]">
      {Array.from({ length: totalSteps }, (_, index) => (
        <div
          key={index}
          className={`box-border w-3/12 h-1 ${
            index < currentStep ? 'bg-[#FF4C46]' : 'bg-[#EEF1F2]'
          }`}
        />
      ))}
    </div>
  );
};
