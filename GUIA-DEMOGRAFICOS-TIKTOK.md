# 📊 Guia de Dados Demográficos - TikTok API

## ✅ **Dados Disponíveis com Scopes Básicos**

### 🔓 **Sempre Disponíveis (Sem Aprovação)**

#### **user.info.basic**
- ✅ `open_id` - ID único do usuário
- ✅ `union_id` - ID único entre apps do mesmo desenvolvedor 
- ✅ `display_name` - Nome de exibição
- ✅ `avatar_url` - URL do avatar (várias resoluções)

#### **user.info.profile** 
- ✅ `username` - Nome de usuário (@username)
- ✅ `bio_description` - Descrição da biografia
- ✅ `is_verified` - Status de verificação (selo azul)
- ✅ `profile_deep_link` - Link direto para o perfil

#### **user.info.stats**
- ✅ `follower_count` - Número de seguidores
- ✅ `following_count` - Número de pessoas seguidas
- ✅ `likes_count` - Total de curtidas recebidas
- ✅ `video_count` - Número total de vídeos

---

## ⚠️ **Dados Demográficos Limitados**

### 🔒 **Via Data Portability API** (Requer Aprovação)

#### **portability.postsandprofile.single**
- 📅 `date_of_birth` - Data de nascimento
- 📧 `email_address` - Email (se fornecido e verificado)
- 📱 `telephone_number` - Telefone (se fornecido e verificado)

#### **Dados de Localização**
- 📍 `Most Recent Location Data` - Localização recente*
- 🌍 `region_code` - Código do país nos vídeos
- 📍 `Location Reviews` - Avaliações de locais

*⚠️ Disponível apenas em algumas regiões

---

## ❌ **Dados NÃO Disponíveis**

### 🚫 **Não Fornecidos pela API Pública**
- ❌ **Gênero** - Não disponível em APIs públicas
- ❌ **Demografia da Audiência** - Apenas para anunciantes
- ❌ **Renda** - Não disponível
- ❌ **Educação** - Não disponível
- ❌ **Estado Civil** - Não disponível

---

## 🎯 **Implementação Atual**

### **Scopes Ativos:**
```javascript
[
  'user.info.basic',    // ✅ Implementado
  'user.info.profile',  // ✅ Implementado  
  'user.info.stats'     // ✅ Implementado
]
```

### **Dados Coletados:**
- 👤 Perfil completo com username e bio
- 📊 Estatísticas completas de engagement
- ✅ Status de verificação
- 🔗 Link direto para o perfil
- 🎨 Avatares em múltiplas resoluções

---

## 🚀 **Próximos Passos para Mais Dados**

### **Opção 1: Data Portability API**
```javascript
// Requer aprovação especial
'portability.postsandprofile.single'
```
**Dados adicionais:**
- Data de nascimento 
- Email/telefone (se fornecidos)
- Histórico de localização

### **Opção 2: Research API**
```javascript
// Para análise de conteúdo público
'research.data.basic'
```
**Dados adicionais:**
- Region code dos vídeos
- Dados demográficos agregados
- Analytics de conteúdo público

### **Opção 3: Commercial Content API**
```javascript
// Para anunciantes verificados
'commercial.content.basic'
```
**Dados adicionais:**
- Demografia da audiência
- Targeting por idade/gênero
- Analytics de campanhas

---

## 📝 **Recomendações**

### **Para Demografia Básica:**
1. ✅ Use os **scopes atuais** (máximo de dados sem aprovação)
2. 🌍 Analise **region_code** dos vídeos para localização aproximada
3. 📊 Use **engagement stats** para inferir demographics indiretos

### **Para Demografia Avançada:**
1. 📋 Solicite aprovação para **Data Portability API**
2. 🔍 Implemente **Research API** para dados públicos agregados
3. 💼 Considere **Commercial Content API** se for anunciante

### **Limitações Importantes:**
- 🔒 TikTok protege dados pessoais sensíveis
- 🌍 Disponibilidade varia por região (GDPR, CCPA)
- 👤 Usuários podem restringir compartilhamento
- ⏱️ Data Portability requer processo de aprovação

---

## 🛡️ **Conformidade e Privacidade**

### **GDPR/LGPD:**
- ✅ Apenas dados explicitamente autorizados
- ✅ Usuário pode revogar acesso a qualquer momento
- ✅ Dados não são armazenados sem consentimento

### **Melhores Práticas:**
- 🔒 Use apenas dados necessários para sua funcionalidade
- 📝 Seja transparente sobre quais dados coleta
- 🗑️ Implemente opções de exclusão de dados
- 🔄 Respeite limites de rate limiting da API

---

*Última atualização: Janeiro 2025*
*Baseado na documentação oficial do TikTok for Developers* 