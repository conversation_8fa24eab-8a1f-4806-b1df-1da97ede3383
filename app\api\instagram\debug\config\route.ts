import { NextRequest, NextResponse } from 'next/server';
import { instagramAPI } from '@/lib/instagram-api';

/**
 * Endpoint de debug para verificar configurações do Instagram
 * GET /api/instagram/debug/config
 */
export async function GET(request: NextRequest) {
  try {
    // Gera uma URL de teste
    const testState = 'debug_test_state';
    const scopes = [
      'instagram_business_basic',
      'instagram_business_content_publish',
      'instagram_business_manage_messages',
      'instagram_business_manage_comments',
      'instagram_business_manage_insights'
    ];
    
    const authUrl = instagramAPI.getAuthorizationUrl(scopes, testState);
    
    return NextResponse.json({
      success: true,
      message: 'Configurações do Instagram carregadas',
      config: {
        appId: process.env.INSTAGRAM_APP_ID ? process.env.INSTAGRAM_APP_ID.substring(0, 10) + '...' : 'NÃO CONFIGURADO',
        redirectUri: process.env.INSTAGRAM_REDIRECT_URI || 'NÃO CONFIGURADO',
        graphApiVersion: process.env.GRAPH_API_VERSION || 'v19.0',
        debugMode: process.env.DEBUG_MODE === 'true',
        environment: process.env.NODE_ENV || 'development'
      },
      testAuthUrl: authUrl,
      instructions: {
        step1: 'Verifique se o App ID está configurado',
        step2: 'Verifique se o Redirect URI está correto',
        step3: 'Configure no Meta Developer Console: ' + (process.env.INSTAGRAM_REDIRECT_URI || 'URL não configurada'),
        step4: 'Teste a autenticação usando o testAuthUrl fornecido'
      },
      troubleshooting: {
        redirectUriMismatch: 'Se der erro de redirect_uri, configure no Meta Developer Console o exato: ' + (process.env.INSTAGRAM_REDIRECT_URI || 'URL não configurada'),
        invalidAppId: 'Verifique se o INSTAGRAM_APP_ID está correto no arquivo .env',
        invalidAppSecret: 'Verifique se o INSTAGRAM_APP_SECRET está correto no arquivo .env',
        scopeIssues: 'Certifique-se que as permissões estão configuradas no Meta Developer Console'
      }
    });

  } catch (error: unknown) {
    console.error('❌ Erro no debug config:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro ao carregar configurações',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      details: {
        envVars: {
          INSTAGRAM_APP_ID: !!process.env.INSTAGRAM_APP_ID,
          INSTAGRAM_APP_SECRET: !!process.env.INSTAGRAM_APP_SECRET,
          INSTAGRAM_REDIRECT_URI: !!process.env.INSTAGRAM_REDIRECT_URI
        }
      }
    }, { status: 500 });
  }
} 