"use client";

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

/**
 * Interface para dados do usuário TikTok
 * Baseada na estrutura da TikTok API v2
 */
interface TikTokUser {
  // Campos básicos (user.info.basic)
  open_id: string;
  union_id?: string;
  display_name: string;
  avatar_url?: string;
  avatar_url_100?: string;
  avatar_large_url?: string;
  
  // Campos de perfil completo (user.info.profile)
  bio_description?: string;
  profile_deep_link?: string;
  is_verified?: boolean;
  username?: string;
  
  // Campos de estatísticas (user.info.stats)
  follower_count?: number;
  following_count?: number;
  likes_count?: number;
  video_count?: number;
}

/**
 * Interface para o estado de autenticação do TikTok
 * Gerencia status, dados do usuário e estados de carregamento
 */
interface TikTokAuthState {
  isAuthenticated: boolean;
  user: TikTokUser | null;
  isLoading: boolean;
  error: string | null;
  authStatus: 'idle' | 'pending' | 'success' | 'error';
  lastAuthAttempt?: string;
}

/**
 * Hook personalizado para gerenciar autenticação do TikTok
 * 
 * Funcionalidades:
 * - Gerenciamento seguro de estado de autenticação
 * - Persistência de dados no localStorage
 * - Processamento de callbacks OAuth
 * - Tratamento robusto de erros
 * - Logging de auditoria
 * - Limpeza automática de URLs
 * 
 * @returns Estado e funções de autenticação
 */
export function useTikTokAuth() {
  const [authState, setAuthState] = useState<TikTokAuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true, // Inicia carregando para verificar localStorage
    error: null,
    authStatus: 'idle'
  });

  const searchParams = useSearchParams();

  useEffect(() => {
    console.log('🔍 useTikTokAuth useEffect executado');
    
    // Obtém parâmetros de retorno da autenticação
    const authResult = searchParams.get('tiktok_auth');
    const username = searchParams.get('tiktok_user');
    const errorMessage = searchParams.get('error_message');
    const tiktokData = searchParams.get('tiktok_data');
    
    console.log('📊 Parâmetros da URL TikTok:', { 
      authResult, 
      username, 
      hasTikTokData: !!tiktokData 
    });

    // Processamento de autenticação bem-sucedida
    if (authResult === 'success' && username) {
      console.log('✅ Processando sucesso de autenticação TikTok');
      
      let userData: TikTokUser = {
        open_id: 'temp_id',
        display_name: username
      };

      // Processa dados completos do TikTok se disponíveis
      if (tiktokData) {
        try {
          const parsedData = JSON.parse(decodeURIComponent(tiktokData));
          console.log('📥 Dados TikTok recebidos:', {
            user_id: parsedData.user?.open_id,
            display_name: parsedData.user?.display_name,
            follower_count: parsedData.user?.follower_count,
            following_count: parsedData.user?.following_count,
            likes_count: parsedData.user?.likes_count,
            video_count: parsedData.user?.video_count
          });

                               userData = {
            // Campos básicos (user.info.basic)
            open_id: parsedData.user.open_id,
            union_id: parsedData.user.union_id,
            display_name: parsedData.user.display_name,
            avatar_url: parsedData.user.avatar_url,
            avatar_url_100: parsedData.user.avatar_url_100,
            avatar_large_url: parsedData.user.avatar_large_url,
            
            // Campos de perfil completo (user.info.profile)
            bio_description: parsedData.user.bio_description,
            profile_deep_link: parsedData.user.profile_deep_link,
            is_verified: parsedData.user.is_verified,
            username: parsedData.user.username,
            
            // Campos de estatísticas (user.info.stats)
            follower_count: parsedData.user.follower_count,
            following_count: parsedData.user.following_count,
            likes_count: parsedData.user.likes_count,
            video_count: parsedData.user.video_count
          };

          // Salva dados seguros no localStorage (sem tokens)
          const secureData = {
            user: userData,
            tokens: {
              // Apenas metadados, nunca tokens reais no localStorage
              expires_in: parsedData.tokens?.expires_in,
              token_type: parsedData.tokens?.token_type,
              has_access_token: !!parsedData.tokens?.access_token,
              has_refresh_token: !!parsedData.tokens?.refresh_token
            },
            scopes: parsedData.scopes,
            authenticated_at: parsedData.authenticated_at,
            platform: 'TikTok',
            security_note: 'Tokens não são armazenados localmente por segurança'
          };

          localStorage.setItem('tiktok_auth', JSON.stringify(secureData));
          console.log('💾 Dados seguros salvos no localStorage');

        } catch (error) {
          console.error('❌ Erro ao processar dados TikTok:', error);
          // Mesmo com erro no parsing, continua com dados básicos
        }
      }

      // Atualiza estado com sucesso
      setAuthState({
        isAuthenticated: true,
        user: userData,
        isLoading: false,
        error: null,
        authStatus: 'success',
        lastAuthAttempt: new Date().toISOString()
      });

      // Se não tem dados completos, salva pelo menos o básico
      if (!tiktokData) {
        const basicData = {
          user: userData,
          tokens: { has_access_token: false },
          scopes: [],
          authenticated_at: new Date().toISOString(),
          platform: 'TikTok'
        };
        localStorage.setItem('tiktok_auth', JSON.stringify(basicData));
      }

      // Limpa parâmetros da URL após processar (segurança)
      if (typeof window !== 'undefined') {
        const url = new URL(window.location.href);
        url.searchParams.delete('tiktok_auth');
        url.searchParams.delete('tiktok_user');
        url.searchParams.delete('tiktok_data');
        window.history.replaceState({}, '', url.toString());
        console.log('🧹 Parâmetros de autenticação limpos da URL');
      }

    } 
    // Processamento de erro de autenticação
    else if (authResult === 'error') {
      const decodedError = errorMessage ? 
        decodeURIComponent(errorMessage) : 
        'Erro na autenticação do TikTok';
      
      console.error('❌ Erro de autenticação TikTok:', decodedError);

      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: decodedError,
        authStatus: 'error',
        lastAuthAttempt: new Date().toISOString()
      });

      // Limpa parâmetros de erro da URL
      if (typeof window !== 'undefined') {
        const url = new URL(window.location.href);
        url.searchParams.delete('tiktok_auth');
        url.searchParams.delete('error_message');
        window.history.replaceState({}, '', url.toString());
      }
    }

    // Verifica dados salvos no localStorage
    if (typeof window !== 'undefined' && !authResult) {
      const savedAuthData = localStorage.getItem('tiktok_auth');
      console.log('💾 Verificando localStorage TikTok:', { hasSavedData: !!savedAuthData });
      
      if (savedAuthData) {
        try {
          const parsedData = JSON.parse(savedAuthData);
          console.log('✅ Dados TikTok recuperados:', { 
            hasUser: !!parsedData.user, 
            display_name: parsedData.user?.display_name,
            authenticated_at: parsedData.authenticated_at
          });
          
          setAuthState({
            isAuthenticated: true,
            user: parsedData.user,
            isLoading: false,
            error: null,
            authStatus: 'success',
            lastAuthAttempt: parsedData.authenticated_at
          });

        } catch (error) {
          console.error('❌ Erro ao carregar dados TikTok salvos:', error);
          localStorage.removeItem('tiktok_auth');
          setAuthState(prev => ({ ...prev, isLoading: false }));
        }
      } else {
        console.log('❌ Nenhum dado TikTok encontrado no localStorage');
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } else if (!authResult) {
      // Se não está processando callback, para o loading
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }

  }, [searchParams]);

  /**
   * Realiza logout e limpa dados de autenticação
   * Remove dados do localStorage e reseta estado
   */
  const logout = () => {
    console.log('🔓 Realizando logout TikTok');
    
    setAuthState({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      error: null,
      authStatus: 'idle'
    });

    // Remove dados salvos com segurança
    if (typeof window !== 'undefined') {
      localStorage.removeItem('tiktok_auth');
      console.log('🗑️ Dados TikTok removidos do localStorage');
    }
  };

  /**
   * Limpa mensagens de erro
   * Útil para remover erros após serem exibidos ao usuário
   */
  const clearError = () => {
    setAuthState(prev => ({
      ...prev,
      error: null,
      authStatus: prev.isAuthenticated ? 'success' : 'idle'
    }));
  };

  /**
   * Força recarregamento dos dados de autenticação
   * Útil para sincronizar estado após mudanças externas
   */
  const refreshAuth = () => {
    if (typeof window !== 'undefined') {
      const savedAuthData = localStorage.getItem('tiktok_auth');
      if (savedAuthData) {
        try {
          const parsedData = JSON.parse(savedAuthData);
          setAuthState(prev => ({
            ...prev,
            user: parsedData.user,
            lastAuthAttempt: parsedData.authenticated_at
          }));
        } catch (error) {
          console.error('❌ Erro ao recarregar dados TikTok:', error);
        }
      }
    }
  };

  return {
    ...authState,
    logout,
    clearError,
    refreshAuth
  };
} 