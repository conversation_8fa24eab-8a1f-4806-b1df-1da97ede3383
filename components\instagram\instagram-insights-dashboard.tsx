"use client";

import { useState, useEffect } from 'react';
import { useInstagramAuth } from '@/hooks/use-instagram-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Eye, Heart, MessageCircle, Share, Save, Users, TrendingUp, 
  BarChart3, Calendar, Clock, Loader2, AlertCircle, 
  Instagram, Play, Timer, Activity
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface InsightMetric {
  name: string;
  title: string;
  description: string;
  value: number;
  period: string;
  total_value: number;
  breakdowns: any[];
}

interface InsightsData {
  metrics: InsightMetric[];
  summary: Record<string, number>;
  timestamp: string;
}

interface AggregatedMetrics {
  total_views: number;
  total_reach: number;
  total_likes: number;
  total_comments: number;
  total_shares: number;
  total_saved: number;
  total_interactions: number;
  average_engagement_rate: string;
}

export function InstagramInsightsDashboard() {
  const { isAuthenticated, user } = useInstagramAuth();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(false);
  const [insights, setInsights] = useState<InsightsData | null>(null);
  const [aggregatedMetrics, setAggregatedMetrics] = useState<AggregatedMetrics | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('day');
  const [selectedType, setSelectedType] = useState('user');
  const [error, setError] = useState<string | null>(null);

  const loadInsights = async (type: string = selectedType, period: string = selectedPeriod) => {
    if (!isAuthenticated || !user) {
      setError('Usuário não autenticado');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Obtém o token de acesso do localStorage
      const authData = localStorage.getItem('instagram_auth');
      if (!authData) {
        throw new Error('Token de acesso não encontrado');
      }

      const parsedAuthData = JSON.parse(authData);
      const accessToken = parsedAuthData.tokens?.access_token;

      if (!accessToken) {
        throw new Error('Token de acesso inválido');
      }

      console.log(`🔍 Carregando insights: tipo=${type}, período=${period}`);

      const response = await fetch(
        `/api/instagram/insights?type=${type}&period=${period}&limit=20&access_token=${accessToken}`
      );

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Erro ao carregar insights');
      }

      if (type === 'all') {
        // Para insights de todas as mídias, temos métricas agregadas
        setAggregatedMetrics(data.insights.aggregated_metrics);
        setInsights({
          metrics: [],
          summary: data.insights.aggregated_metrics,
          timestamp: data.timestamp
        });
      } else {
        setInsights(data.insights);
        setAggregatedMetrics(null);
      }

      toast({
        title: "Insights carregados!",
        description: `Dados de insights do tipo "${type}" obtidos com sucesso.`,
      });

    } catch (error: any) {
      console.error('❌ Erro ao carregar insights:', error);
      setError(error.message);
      
      toast({
        variant: "destructive",
        title: "Erro ao carregar insights",
        description: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated && user) {
      loadInsights();
    }
  }, [isAuthenticated, user]);

  const getMetricIcon = (metricName: string) => {
    switch (metricName) {
      case 'views': return Eye;
      case 'likes': return Heart;
      case 'comments': return MessageCircle;
      case 'shares': return Share;
      case 'saved': return Save;
      case 'reach': return Users;
      case 'follower_count': return Users;
      case 'website_clicks': return Activity;
      case 'accounts_engaged': return TrendingUp;
      case 'ig_reels_avg_watch_time': return Timer;
      case 'ig_reels_video_view_total_time': return Play;
      default: return BarChart3;
    }
  };

  const formatValue = (value: number, metricName: string) => {
    if (metricName.includes('time')) {
      // Formata tempo em segundos para minutos:segundos
      const minutes = Math.floor(value / 60);
      const seconds = value % 60;
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  if (!isAuthenticated) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Instagram className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              Conecte sua conta do Instagram para ver os insights
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com controles */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400 rounded-lg flex items-center justify-center">
                  <Instagram className="w-5 h-5 text-white" />
                </div>
                Instagram Insights
              </CardTitle>
              <CardDescription>
                Métricas detalhadas do seu perfil e conteúdos
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-3">
              <Select value={selectedType} onValueChange={(value) => {
                setSelectedType(value);
                loadInsights(value, selectedPeriod);
              }}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">Perfil</SelectItem>
                  <SelectItem value="all">Todas as Mídias</SelectItem>
                  <SelectItem value="historical">Histórico</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedPeriod} onValueChange={(value) => {
                setSelectedPeriod(value);
                loadInsights(selectedType, value);
              }}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Hoje</SelectItem>
                  <SelectItem value="week">7 dias</SelectItem>
                  <SelectItem value="days_28">28 dias</SelectItem>
                  <SelectItem value="month">Mês</SelectItem>
                  <SelectItem value="lifetime">Total</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                onClick={() => loadInsights(selectedType, selectedPeriod)}
                disabled={isLoading}
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <>
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Atualizar
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Erro */}
      {error && (
        <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
          <AlertCircle className="w-4 h-4 text-red-500" />
          <AlertDescription className="text-red-700 dark:text-red-300">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Carregando insights...
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Métricas Principais */}
      {(insights || aggregatedMetrics) && !isLoading && (
        <>
          {/* Resumo das Métricas */}
          {selectedType === 'all' && aggregatedMetrics && (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Eye className="h-4 w-4 text-blue-500" />
                    <div className="ml-2">
                      <p className="text-sm font-medium leading-none">
                        Total de Visualizações
                      </p>
                      <p className="text-2xl font-bold text-blue-600">
                        {formatValue(aggregatedMetrics.total_views, 'views')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-green-500" />
                    <div className="ml-2">
                      <p className="text-sm font-medium leading-none">
                        Alcance Total
                      </p>
                      <p className="text-2xl font-bold text-green-600">
                        {formatValue(aggregatedMetrics.total_reach, 'reach')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Heart className="h-4 w-4 text-red-500" />
                    <div className="ml-2">
                      <p className="text-sm font-medium leading-none">
                        Total de Curtidas
                      </p>
                      <p className="text-2xl font-bold text-red-600">
                        {formatValue(aggregatedMetrics.total_likes, 'likes')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 text-purple-500" />
                    <div className="ml-2">
                      <p className="text-sm font-medium leading-none">
                        Taxa de Engajamento
                      </p>
                      <p className="text-2xl font-bold text-purple-600">
                        {aggregatedMetrics.average_engagement_rate}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Métricas Detalhadas */}
          {insights && insights.metrics && insights.metrics.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Métricas Detalhadas</CardTitle>
                <CardDescription>
                  Período: {selectedPeriod} • Atualizado em {new Date(insights.timestamp).toLocaleString('pt-BR')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {insights.metrics.map((metric, index) => {
                    const IconComponent = getMetricIcon(metric.name);
                    return (
                      <div key={index} className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <div className="flex items-center gap-3 mb-2">
                          <IconComponent className="w-5 h-5 text-blue-500" />
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{metric.title}</h4>
                            <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                              {metric.description}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-2xl font-bold">
                            {formatValue(metric.value, metric.name)}
                          </span>
                          <Badge variant="outline">
                            {metric.period}
                          </Badge>
                        </div>

                        {/* Breakdowns se disponível */}
                        {metric.breakdowns && metric.breakdowns.length > 0 && (
                          <div className="mt-3 pt-3 border-t">
                            <p className="text-xs text-gray-500 mb-2">Detalhamento:</p>
                            {metric.breakdowns[0]?.results?.slice(0, 3).map((breakdown: any, idx: number) => (
                              <div key={idx} className="flex justify-between text-xs">
                                <span className="capitalize">{breakdown.dimension_values?.[0]}</span>
                                <span className="font-medium">{breakdown.value}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Informações Adicionais */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <Calendar className="w-4 h-4" />
                <span>Última atualização: {new Date().toLocaleString('pt-BR')}</span>
                <Clock className="w-4 h-4 ml-4" />
                <span>Dados podem ter até 48h de atraso</span>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Estado vazio */}
      {!insights && !aggregatedMetrics && !isLoading && !error && (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Clique em &quot;Atualizar&quot; para carregar os insights
              </p>
              <Button onClick={() => loadInsights()}>
                <BarChart3 className="w-4 h-4 mr-2" />
                Carregar Insights
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 