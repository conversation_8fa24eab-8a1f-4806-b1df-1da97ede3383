import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

/**
 * Webhook de Deauthorize do Instagram/Facebook
 * POST /api/auth/deauthorize
 * 
 * Este endpoint é chamado automaticamente quando:
 * - Usu<PERSON><PERSON> remove o app das configurações do Instagram/Facebook
 * - Usuário revoga permissões específicas
 * - Instagram/Facebook remove o app por violação de políticas
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.formData();
    const signedRequest = body.get('signed_request') as string;

    if (!signedRequest) {
      console.log('❌ Webhook deauthorize recebido sem signed_request');
      return NextResponse.json({
        success: false,
        error: 'missing_signed_request',
        message: 'Parâmetro signed_request é obrigatório'
      }, { status: 400 });
    }

    // Parse do signed request
    const userData = parseSignedRequest(signedRequest);
    
    if (!userData) {
      console.log('❌ Signed request inválido no webhook deauthorize');
      return NextResponse.json({
        success: false,
        error: 'invalid_signed_request',
        message: 'Signed request inválido'
      }, { status: 400 });
    }

    const userId = userData.user_id;
    const issuedAt = userData.issued_at;
    const algorithm = userData.algorithm;

    console.log('📨 Webhook deauthorize recebido:', {
      user_id: userId,
      issued_at: new Date(issuedAt * 1000).toISOString(),
      algorithm
    });

    // Processa a revogação de autorização
    await processDeauthorization(userId);

    // Resposta de sucesso para o Instagram/Facebook
    return NextResponse.json({
      success: true,
      message: 'Deauthorization processed successfully'
    });

  } catch (error) {
    console.error('❌ Erro no webhook deauthorize:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * Endpoint GET para verificação de saúde do webhook
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const hubMode = searchParams.get('hub.mode');
  const hubChallenge = searchParams.get('hub.challenge');
  const hubVerifyToken = searchParams.get('hub.verify_token');

  // Verificação do webhook para configuração inicial
  if (hubMode === 'subscribe') {
    const expectedToken = process.env.INSTAGRAM_WEBHOOK_VERIFY_TOKEN || 'aface_webhook_token_2024';
    
    if (hubVerifyToken === expectedToken) {
      console.log('✅ Webhook deauthorize verificado com sucesso');
      return new NextResponse(hubChallenge, { status: 200 });
    } else {
      console.log('❌ Token de verificação do webhook inválido');
      return new NextResponse('Forbidden', { status: 403 });
    }
  }

  return NextResponse.json({
    success: true,
    message: 'Deauthorize webhook endpoint ativo',
    timestamp: new Date().toISOString()
  });
}

/**
 * Parse do signed request do Instagram/Facebook
 */
function parseSignedRequest(signedRequest: string) {
  try {
    const [encodedSig, payload] = signedRequest.split('.', 2);
    const secret = process.env.INSTAGRAM_CLIENT_SECRET;

    if (!secret) {
      throw new Error('INSTAGRAM_CLIENT_SECRET não configurado');
    }

    // Decodifica a assinatura e payload
    const sig = base64UrlDecode(encodedSig);
    const data = JSON.parse(base64UrlDecode(payload).toString());

    // Verifica a assinatura
    const expectedSig = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest();

    if (!crypto.timingSafeEqual(sig, expectedSig)) {
      throw new Error('Assinatura inválida no signed request');
    }

    return data;
  } catch (error) {
    console.error('❌ Erro ao fazer parse do signed request:', error);
    return null;
  }
}

/**
 * Decodifica base64 URL-safe
 */
function base64UrlDecode(input: string): Buffer {
  // Adiciona padding se necessário
  input += '='.repeat((4 - input.length % 4) % 4);
  // Substitui caracteres URL-safe
  input = input.replace(/-/g, '+').replace(/_/g, '/');
  return Buffer.from(input, 'base64');
}

/**
 * Processa a revogação de autorização
 */
async function processDeauthorization(userId: string) {
  try {
    console.log(`🔐 Processando deauthorize para usuário: ${userId}`);

    // 1. Marca os tokens como revogados no banco de dados
    // await markTokensAsRevoked(userId);

    // 2. Remove dados em cache relacionados ao usuário
    // await clearUserCache(userId);

    // 3. Remove sessões ativas
    // await terminateUserSessions(userId);

    // 4. Registra o evento de deauthorize para auditoria
    await logDeauthorizeEvent(userId);

    // 5. Notifica sistemas internos sobre a revogação
    // await notifyInternalSystems(userId, 'deauthorized');

    // 6. Opcionalmente, notifica o usuário por email
    // await sendDeauthorizeNotification(userId);

    console.log(`✅ Deauthorize processado com sucesso para usuário: ${userId}`);

  } catch (error) {
    console.error(`❌ Erro ao processar deauthorize para usuário ${userId}:`, error);
    throw error;
  }
}

/**
 * Registra evento de deauthorize para auditoria
 */
async function logDeauthorizeEvent(userId: string) {
  try {
    // Em um ambiente real, isso seria salvo no banco de dados
    const deauthorizeRecord = {
      user_id: userId,
      event_type: 'deauthorize',
      timestamp: new Date().toISOString(),
      source: 'instagram_webhook',
      action_taken: 'tokens_revoked',
      status: 'processed'
    };

    console.log('📝 Registro de deauthorize:', deauthorizeRecord);

    // TODO: Salvar no banco de dados
    // await database.auditLogs.create(deauthorizeRecord);

  } catch (error) {
    console.error('❌ Erro ao registrar evento de deauthorize:', error);
    // Não falha o processo principal se o log falhar
  }
} 