"use client";

import { useState } from 'react';
import { useInstagramAuth } from '@/hooks/use-instagram-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';

import { 
  TestTube, Play, CheckCircle, XCircle, AlertCircle, Loader2, 
  User, BarChart3, Image, TrendingUp, Clock, Eye, Activity
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TestResult {
  user_profile: any;
  user_insights: any;
  media_list: any;
  media_insights: any;
  available_metrics: any;
  errors: any[];
  timestamp: string;
}

interface TestSummary {
  user_profile_loaded: boolean;
  insights_periods_tested: number;
  media_found: number;
  media_insights_tested: number;
  metric_groups_tested: number;
  total_errors: number;
}

export function InstagramTestLab() {
  const { isAuthenticated, user } = useInstagramAuth();
  const { toast } = useToast();
  
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult | null>(null);
  const [testSummary, setTestSummary] = useState<TestSummary | null>(null);
  const [error, setError] = useState<string | null>(null);

  const runTests = async () => {
    if (!isAuthenticated || !user) {
      setError('Usuário não autenticado');
      return;
    }

    try {
      setIsRunning(true);
      setError(null);
      setTestResults(null);
      setTestSummary(null);

      // Obtém o token de acesso do localStorage
      const authData = localStorage.getItem('instagram_auth');
      if (!authData) {
        throw new Error('Token de acesso não encontrado');
      }

      const parsedAuthData = JSON.parse(authData);
      const accessToken = parsedAuthData.tokens?.access_token;

      if (!accessToken) {
        throw new Error('Token de acesso inválido');
      }

      console.log('🧪 Iniciando laboratório de testes...');

      const response = await fetch(
        `/api/instagram/test-insights?access_token=${accessToken}`
      );

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Erro ao executar testes');
      }

      setTestResults(data.results);
      setTestSummary(data.summary);

      toast({
        title: "Testes concluídos!",
        description: `${data.summary.metric_groups_tested} grupos de métricas testados, ${data.summary.total_errors} erros encontrados.`,
      });

    } catch (error: any) {
      console.error('❌ Erro ao executar testes:', error);
      setError(error.message);
      
      toast({
        variant: "destructive",
        title: "Erro nos testes",
        description: error.message,
      });
    } finally {
      setIsRunning(false);
    }
  };

  const formatValue = (value: any) => {
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  if (!isAuthenticated) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <TestTube className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              Conecte sua conta do Instagram para executar os testes
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com controles */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <TestTube className="w-5 h-5 text-white" />
                </div>
                Laboratório de Testes Instagram
              </CardTitle>
              <CardDescription>
                Teste completo das permissões: instagram_basic, instagram_manage_insights, read_insights
              </CardDescription>
            </div>
            
            <Button 
              onClick={runTests}
              disabled={isRunning}
              size="lg"
              className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600"
            >
              {isRunning ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Executando Testes...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Executar Todos os Testes
                </>
              )}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Resumo dos Testes */}
      {testSummary && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-4 w-4 text-blue-500" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">
                    Perfil de Usuário
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {testSummary.user_profile_loaded ? '✅ Carregado' : '❌ Falhou'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-4 w-4 text-green-500" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">
                    Períodos de Insights
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {testSummary.insights_periods_tested}/5
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Image className="h-4 w-4 text-purple-500" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">
                    Mídias Encontradas
                  </p>
                  <p className="text-2xl font-bold text-purple-600">
                    {testSummary.media_found}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 text-orange-500" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">
                    Grupos de Métricas
                  </p>
                  <p className="text-2xl font-bold text-orange-600">
                    {testSummary.metric_groups_tested}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="h-4 w-4 text-pink-500" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">
                    Insights de Mídia
                  </p>
                  <p className="text-2xl font-bold text-pink-600">
                    {testSummary.media_insights_tested}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertCircle className={`h-4 w-4 ${testSummary.total_errors > 0 ? 'text-red-500' : 'text-green-500'}`} />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">
                    Erros Encontrados
                  </p>
                  <p className={`text-2xl font-bold ${testSummary.total_errors > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {testSummary.total_errors}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Erro */}
      {error && (
        <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
          <AlertCircle className="w-4 h-4 text-red-500" />
          <AlertDescription className="text-red-700 dark:text-red-300">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isRunning && (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Executando testes completos das permissões...
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Isso pode levar alguns segundos
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resultados Detalhados */}
      {testResults && (
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="profile">Perfil</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="media">Mídias</TabsTrigger>
            <TabsTrigger value="metrics">Métricas</TabsTrigger>
            <TabsTrigger value="errors">Erros</TabsTrigger>
          </TabsList>

          {/* Tab Perfil */}
          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Perfil do Usuário (instagram_basic)</CardTitle>
                <CardDescription>
                  Dados básicos do perfil obtidos com sucesso
                </CardDescription>
              </CardHeader>
              <CardContent>
                {testResults.user_profile ? (
                  <div className="space-y-3">
                    <div className="grid gap-3 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium">ID:</label>
                        <p className="font-mono text-sm">{testResults.user_profile.id}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Username:</label>
                        <p className="font-medium">@{testResults.user_profile.username}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Nome:</label>
                        <p>{testResults.user_profile.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Tipo de Conta:</label>
                        <p>{testResults.user_profile.account_type || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Seguidores:</label>
                        <p>{testResults.user_profile.followers_count?.toLocaleString() || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Mídia Count:</label>
                        <p>{testResults.user_profile.media_count?.toLocaleString() || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <Alert>
                    <XCircle className="w-4 h-4" />
                    <AlertDescription>
                      Não foi possível carregar o perfil do usuário
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Insights */}
          <TabsContent value="insights">
            <Card>
              <CardHeader>
                <CardTitle>Insights por Período (instagram_manage_insights)</CardTitle>
                <CardDescription>
                  Testes de insights para diferentes períodos de tempo
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(testResults.user_insights).map(([period, insights]: [string, any]) => (
                    <div key={period} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium capitalize">{period}</h4>
                        <Badge variant="outline">
                          {insights.metrics?.length || 0} métricas
                        </Badge>
                      </div>
                      
                      {insights.summary && Object.keys(insights.summary).length > 0 ? (
                        <div className="grid gap-3 md:grid-cols-3">
                          {Object.entries(insights.summary).map(([metric, value]: [string, any]) => (
                            <div key={metric} className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                                {metric.replace('_', ' ')}
                              </p>
                              <p className="font-bold text-lg">{formatValue(value)}</p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm">Nenhum dado disponível para este período</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Mídias */}
          <TabsContent value="media">
            <Card>
              <CardHeader>
                <CardTitle>Lista de Mídias</CardTitle>
                <CardDescription>
                  Mídias encontradas no perfil do usuário
                </CardDescription>
              </CardHeader>
              <CardContent>
                {testResults.media_list?.data && testResults.media_list.data.length > 0 ? (
                  <div className="space-y-4">
                    {testResults.media_list.data.map((media: any) => (
                      <div key={media.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge variant={media.media_type === 'IMAGE' ? 'default' : 'secondary'}>
                              {media.media_type}
                            </Badge>
                            <span className="font-mono text-sm text-gray-500">{media.id}</span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(media.timestamp).toLocaleDateString('pt-BR')}
                          </div>
                        </div>
                        
                        {media.caption && (
                          <p className="text-sm text-gray-700 dark:text-gray-300 mb-2 line-clamp-2">
                            {media.caption}
                          </p>
                        )}
                        
                        <div className="flex gap-4 text-sm">
                          <span>❤️ {media.like_count || 0}</span>
                          <span>💬 {media.comments_count || 0}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Alert>
                    <AlertCircle className="w-4 h-4" />
                    <AlertDescription>
                      Nenhuma mídia encontrada no perfil
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Métricas */}
          <TabsContent value="metrics">
            <Card>
              <CardHeader>
                <CardTitle>Teste de Métricas</CardTitle>
                <CardDescription>
                  Diferentes grupos de métricas testadas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(testResults.available_metrics).map(([groupName, result]: [string, any]) => (
                    <div key={groupName} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium capitalize">{groupName.replace('_', ' ')}</h4>
                        <div className="flex items-center gap-2">
                          {result.success ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                          <Badge variant={result.success ? "default" : "destructive"}>
                            {result.success ? 'Sucesso' : 'Falhou'}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="text-sm">
                        <p className="text-gray-600 dark:text-gray-400 mb-2">
                          Métricas: {result.metrics?.join(', ') || 'N/A'}
                        </p>
                        
                        {result.success && result.result?.summary && (
                          <div className="grid gap-2 md:grid-cols-3 mt-3">
                            {Object.entries(result.result.summary).map(([metric, value]: [string, any]) => (
                              <div key={metric} className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded">
                                <p className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                                  {metric.replace('_', ' ')}
                                </p>
                                <p className="font-medium">{formatValue(value)}</p>
                              </div>
                            ))}
                          </div>
                        )}
                        
                        {!result.success && (
                          <p className="text-red-600 dark:text-red-400 text-sm mt-2">
                            Erro: {result.error}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Erros */}
          <TabsContent value="errors">
            <Card>
              <CardHeader>
                <CardTitle>Erros Encontrados</CardTitle>
                <CardDescription>
                  Lista de erros durante os testes
                </CardDescription>
              </CardHeader>
              <CardContent>
                {testResults.errors && testResults.errors.length > 0 ? (
                  <div className="space-y-3">
                    {testResults.errors.map((error: any, index: number) => (
                      <Alert key={index} className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
                        <XCircle className="w-4 h-4 text-red-500" />
                        <AlertDescription className="text-red-700 dark:text-red-300">
                          <strong>{error.test}:</strong> {error.error}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                ) : (
                  <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <AlertDescription className="text-green-700 dark:text-green-300">
                      🎉 Nenhum erro encontrado! Todos os testes passaram com sucesso.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Estado vazio */}
      {!testResults && !isRunning && !error && (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <TestTube className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Execute os testes para ver resultados detalhados
              </p>
              <p className="text-sm text-gray-500">
                Os testes verificarão todas as permissões do Instagram
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 