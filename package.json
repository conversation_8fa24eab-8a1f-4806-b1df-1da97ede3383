{"name": "aface-company-nextjs", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.56.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "express-session": "^1.18.2", "framer-motion": "^12.23.6", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next": "^15.1.6", "next-themes": "^0.4.4", "nuqs": "^2.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.4", "recharts": "^2.15.4", "sonner": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.17.0", "@types/node": "^20.17.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "^15.1.6", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.5.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.3.5", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1"}}