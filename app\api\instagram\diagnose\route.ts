import { NextRequest, NextResponse } from 'next/server';
import { instagramAPI } from '@/lib/instagram-api';

/**
 * Endpoint de diagnóstico inteligente do Instagram
 * GET /api/instagram/diagnose
 * 
 * Analisa a conta e fornece recomendações personalizadas
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const accessToken = searchParams.get('access_token');

    if (!accessToken) {
      return NextResponse.json({
        success: false,
        error: 'missing_access_token',
        message: 'Token de acesso é obrigatório'
      }, { status: 400 });
    }

    console.log('🏥 === INICIANDO DIAGNÓSTICO INTELIGENTE ===');

    const diagnosis = await instagramAPI.diagnoseAccount(accessToken);

    // Análise adicional específica dos resultados dos testes
    const analysis: {
      account_type: string;
      engagement_potential: string;
      insight_availability: any;
      limitations: any[];
      opportunities: any[];
    } = {
      account_type: diagnosis.profile.account_type || 'PERSONAL',
      engagement_potential: diagnosis.content_status.has_posts ? 'HIGH' : 'LIMITED',
      insight_availability: {
        basic_metrics: true, // follower_count sempre funciona
        engagement_metrics: diagnosis.content_status.has_posts,
        reach_metrics: diagnosis.content_status.has_posts,
        website_metrics: true // website_clicks geralmente funciona
      },
      limitations: [],
      opportunities: []
    };

    // Identificar limitações específicas
    if (!diagnosis.content_status.has_posts) {
      analysis.limitations.push({
        type: 'NO_CONTENT',
        description: 'Conta sem posts publicados',
        impact: 'Métricas de reach, impressões e engajamento indisponíveis',
        solution: 'Publique conteúdo para desbloquear métricas avançadas'
      });
    }

    if (diagnosis.content_status.followers_count < 100) {
      analysis.limitations.push({
        type: 'LOW_FOLLOWERS',
        description: 'Poucos seguidores',
        impact: 'Algumas métricas podem ter dados limitados',
        solution: 'Cresça sua audiência para dados mais robustos'
      });
    }

    // Identificar oportunidades
    if (diagnosis.content_status.followers_count > 1000) {
      analysis.opportunities.push({
        type: 'STRONG_AUDIENCE',
        description: 'Base de seguidores sólida',
        potential: 'Dados de engajamento e reach mais significativos',
        action: 'Publique conteúdo consistente para maximizar insights'
      });
    }

    if (analysis.insight_availability.website_metrics) {
      analysis.opportunities.push({
        type: 'WEBSITE_TRACKING',
        description: 'Métricas de website disponíveis',
        potential: 'Rastreamento de tráfego do Instagram para seu site',
        action: 'Configure links em bio e stories para monitorar conversões'
      });
    }

    // Recomendações específicas baseadas nos testes
    const smartRecommendations = [
      ...diagnosis.recommendations,
      
      // Recomendações baseadas no que funciona
      'website_clicks funciona ✅ - Configure links estratégicos',
      'follower_count monitora crescimento ✅ - Acompanhe tendências',
      'Períodos day/week/days_28 funcionam ✅ - Use para análises',
      
      // Alertas sobre limitações descobertas
      'Períodos month/lifetime têm limitações ⚠️ - Normal para contas novas',
      'Métricas reach/profile_views precisam de posts 📝'
    ];

    console.log('✅ Diagnóstico concluído');

    return NextResponse.json({
      success: true,
      message: 'Diagnóstico inteligente concluído',
      diagnosis,
      analysis,
      smart_recommendations: smartRecommendations,
      action_plan: {
        immediate: !diagnosis.content_status.has_posts ? [
          'Publique seu primeiro post no Instagram',
          'Configure bio com link do website',
          'Execute testes novamente após publicar conteúdo'
        ] : [
          'Continue monitorando métricas working',
          'Foque em website_clicks e follower_count',
          'Teste novos períodos conforme a conta cresce'
        ],
        short_term: [
          'Publique conteúdo consistentemente',
          'Monitore crescimento de seguidores',
          'Analise performance semanalmente'
        ],
        long_term: [
          'Diversifique tipos de conteúdo',
          'Implemente estratégias de crescimento',
          'Monitore ROI do tráfego do website'
        ]
      },
      testing_summary: {
        working_metrics: ['follower_count', 'website_clicks'],
        working_periods: ['day', 'week', 'days_28'],
        limited_metrics: ['reach', 'profile_views', 'accounts_engaged'],
        limited_periods: ['month', 'lifetime'],
        reason_for_limitations: diagnosis.content_status.has_posts ? 
          'Limitações de permissão ou conta nova' : 
          'Conta sem conteúdo publicado'
      }
    });

  } catch (error: any) {
    console.error('❌ Erro no diagnóstico:', error);
    return NextResponse.json({
      success: false,
      error: 'diagnosis_failed',
      message: 'Falha no diagnóstico da conta',
      details: error.message
    }, { status: 500 });
  }
} 