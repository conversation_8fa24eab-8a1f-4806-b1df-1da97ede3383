import React from 'react';
import { ProgressBar } from '@/components/signup/ProgressBar';
import { SocialSignupButtons } from '@/components/signup/SocialSignupButtons';
import { SignupForm } from '@/components/signup/SignupForm';
import { LeftSideBranding } from '@/components/signup/LeftSideBranding';

export default function Home() {
  return (
    <div className="h-screen w-screen overflow-hidden bg-neutral-100 flex justify-center items-center max-md:flex-col">
      <div className="h-full w-full flex bg-[#FDFDFD] max-md:flex-col">
        <LeftSideBranding />
        
        <main className="flex-1 h-full overflow-y-auto bg-[#FDFDFD] px-16 py-[66px] max-md:order-1 max-md:p-8 max-sm:px-4 max-sm:py-6 flex items-center justify-center">
          <div className="w-full max-w-[423px] mx-auto">
            <div className="text-[#5C5C5C] text-base font-normal leading-6 tracking-[-0.16px] mb-[34px]">
              Etapa 1 de 4
            </div>
            
            <ProgressBar currentStep={1} totalSteps={4} />
            
            <h1 className="text-[#1C1C1C] text-4xl font-bold leading-[48px] tracking-[-0.16px] mb-[72px] max-md:text-[28px] max-md:leading-9 max-md:mb-12 max-sm:text-2xl max-sm:leading-8">
              Vamos criar sua conta
            </h1>
            
            <div className="w-full  mx-auto max-md:max-w-sm">
              <p className="text-[#1C1C1C] text-base font-normal leading-6 tracking-[-0.16px] mb-8">
                Cadastre-se com o Google para criar sua conta e adicionar sua primeira conta em mídia social em uma única etapa
              </p>
              
              <SocialSignupButtons />
              
              <div className="flex justify-center items-center gap-[11.36px] mb-6 py-0.5">
                <div className="flex-1 h-0.5 border-t-[#EBEBEB] border-t border-solid" />
                <div className="px-3 text-[#1C1C1C] text-center text-base font-normal leading-6 tracking-[-0.16px] bg-[#FDFDFD]">
                  ou
                </div>
                <div className="flex-1 h-0.5 border-t-[#EBEBEB] border-t border-solid" />
              </div>
              
              <SignupForm />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
} 