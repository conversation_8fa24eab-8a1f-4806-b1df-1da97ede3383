"use client";

import * as React from "react";
import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";
import { AuthUser } from "@/types/auth";

import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import {
  RiSlowDownLine,
  RiTeamLine,
  RiBarChartBoxLine,
  RiSecurePaymentLine,
  RiCodeSSlashLine,
  RiSettingsLine,
  RiLinksLine,
  RiDatabase2Line,
  RiMailLine,
  RiPhoneLine,
} from "@remixicon/react";

// Dados da navegação para AFace Company
const getNavigationData = (user: AuthUser | null) => ({
  user: {
    name: user?.email?.split('@')[0] || 'Usu<PERSON><PERSON>',
    email: user?.email || '<EMAIL>',
    avatar: user?.avatar || '/logo-afc.svg',
  },
  navMain: [
    {
      title: "Dashboard",
      items: [
        {
          title: "Visão Geral",
          url: "/dashboard",
          icon: RiSlowDownLine,
          isActive: true,
        }
      ],
    },
   
  ],
});

function SidebarLogo() {
  return (
    <div className="flex gap-2 px-2 group-data-[collapsible=icon]:px-0 transition-[padding] duration-200 ease-in-out">
      <Link className="group/logo inline-flex items-center" href="/dashboard">
        <span className="sr-only">AFace Company Logo</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
          width="36"
          height="36"
          viewBox="0 0 402.48 402.48"
          className="size-9 group-data-[collapsible=icon]:size-8 transition-[width,height] duration-200 ease-in-out"
        >
          <defs>
            <linearGradient id="grad1" x1="0" y1="201.24" x2="402.48" y2="201.24" gradientUnits="userSpaceOnUse">
              <stop offset="0" stopColor="#fa12e3"/>
              <stop offset="1" stopColor="gold"/>
            </linearGradient>
            <linearGradient id="grad2" x1="104.43" y1="357.24" x2="287.43" y2="53.24" gradientUnits="userSpaceOnUse">
              <stop offset=".08" stopColor="#fa12e3"/>
              <stop offset=".76" stopColor="gold"/>
            </linearGradient>
            <linearGradient id="grad3" x1="43.24" y1="320.41" x2="226.24" y2="16.41" xlinkHref="#grad1"/>
            <linearGradient id="grad4" x1="149.81" y1="384.56" x2="332.81" y2="80.56" gradientUnits="userSpaceOnUse">
              <stop offset="0" stopColor="#fa12e3"/>
              <stop offset=".99" stopColor="gold"/>
            </linearGradient>
            <linearGradient id="grad5" x1="187.29" y1="407.12" x2="370.29" y2="103.12" xlinkHref="#grad1"/>
          </defs>
          <g>
            <rect 
              fill="#fff" 
              stroke="url(#grad1)" 
              strokeMiterlimit="10" 
              strokeWidth="3" 
              x="1.5" 
              y="1.5" 
              width="399.48" 
              height="399.48" 
              rx="92.98" 
              ry="92.98"
            />
            <g>
              <path fill="url(#grad2)" d="m269.61,69.81l-40.33,35.1-18.64-21.84,59.05-51.4,58.58,51.44-18.73,21.76-39.94-35.06Z"/>
              <path fill="url(#grad3)" d="m71.85,237.2c-14.66-14.28-21.99-32.04-21.99-53.28s7.33-39.04,21.99-53.42,32.79-21.57,54.41-21.57c15.6,0,29.55,4.04,41.86,12.12s21.38,19.17,27.2,33.26l-27.34,9.3c-3.76-8.46-9.3-15.12-16.63-20.01-7.33-4.88-15.69-7.33-25.09-7.33-12.97,0-23.87,4.56-32.7,13.67-8.84,9.12-13.25,20.44-13.25,33.97s4.41,24.81,13.25,33.83c8.83,9.02,19.73,13.53,32.7,13.53,9.4,0,17.8-2.4,25.23-7.19,7.42-4.79,12.92-11.51,16.49-20.16l27.34,9.3c-5.83,14.1-14.9,25.19-27.2,33.26-12.31,8.08-26.26,12.12-41.86,12.12-21.61,0-39.75-7.14-54.41-21.42v.02Z"/>
              <path fill="url(#grad4)" d="m269.46,108.94c23.12,0,41.91,8.27,56.38,24.81,14.47,16.54,20.39,36.55,17.76,60.04h-119.24c2.07,11.65,7.42,21.1,16.07,28.33,8.64,7.24,19.07,10.85,31.29,10.85,9.4,0,17.8-2.3,25.23-6.91,7.42-4.6,13.2-10.85,17.34-18.75l24.81,10.99c-6.39,12.22-15.5,21.99-27.34,29.32s-25.47,10.99-40.88,10.99c-21.61,0-39.75-7.14-54.41-21.42s-21.99-32.04-21.99-53.28,7.19-39.04,21.57-53.42c14.38-14.38,32.18-21.57,53.42-21.57v.02Zm0,25.65c-10.9,0-20.34,3.34-28.33,10.01-7.99,6.67-13.3,15.36-15.93,26.08h88.52c-2.45-10.71-7.66-19.4-15.65-26.08-7.99-6.67-17.53-10.01-28.61-10.01Z"/>
              <path fill="url(#grad5)" d="m212.47,349.32c-15.83,0-31.7-2.76-46.64-8.18l8.86-24.44c16.08,5.83,33.51,7.87,50.38,5.89,16.08-1.88,31.82-7.41,45.5-15.99,13.02-8.18,24.43-19.22,32.97-31.93,1.35-2.01,2.52-3.88,3.58-5.69l22.47,13.09c-1.33,2.29-2.79,4.61-4.45,7.09-10.57,15.72-24.65,29.37-40.74,39.47-16.93,10.62-36.40,17.46-56.29,19.79-5.18.61-10.40.91-15.62.91Z"/>
            </g>
          </g>
        </svg>
        <div className="group-data-[collapsible=icon]:hidden ml-2">
       
        </div>
      </Link>
    </div>
  );
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth();
  const data = getNavigationData(user);

  return (
    <Sidebar collapsible="icon" variant="inset" {...props}>
      <SidebarHeader className="h-16 max-md:mt-2 mb-2 justify-center">
        <SidebarLogo />
      </SidebarHeader>
      <SidebarContent className="-mt-2">
        {data.navMain.map((section) => (
          <SidebarGroup key={section.title}>
            <SidebarGroupLabel className="uppercase text-muted-foreground/65">
              {section.title}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {section.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      className="group/menu-button group-data-[collapsible=icon]:px-[5px]! font-medium gap-3 h-9 [&>svg]:size-auto"
                      tooltip={item.title}
                      isActive={item.isActive}
                    >
                      <Link href={item.url}>
                        {item.icon && (
                          <item.icon
                            className="text-muted-foreground/65 group-data-[active=true]/menu-button:text-primary"
                            size={22}
                            aria-hidden="true"
                          />
                        )}
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
