import { NextRequest, NextResponse } from 'next/server';
import { instagramAPI } from '@/lib/instagram-api';

/**
 * Endpoint de teste completo para Instagram Insights
 * GET /api/instagram/test-insights
 * 
 * Testa TODAS as funcionalidades das permissões:
 * - instagram_basic
 * - instagram_manage_insights  
 * - read_insights
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const accessToken = searchParams.get('access_token');

    if (!accessToken) {
      return NextResponse.json({
        success: false,
        error: 'missing_access_token',
        message: 'Token de acesso é obrigatório'
      }, { status: 400 });
    }

    console.log('🧪 === INICIANDO LABORATÓRIO DE TESTES INSTAGRAM ===');

    const testResults: {
      user_profile: any;
      user_insights: any;
      media_list: any;
      media_insights: any;
      available_metrics: any;
      errors: any[];
      timestamp: string;
    } = {
      user_profile: null,
      user_insights: {},
      media_list: null,
      media_insights: {},
      available_metrics: {},
      errors: [],
      timestamp: new Date().toISOString()
    };

    // === TESTE 1: PERFIL DO USUÁRIO (instagram_basic) ===
    try {
      console.log('🔍 Teste 1: Obtendo perfil completo do usuário...');
      const userProfile = await instagramAPI.getUserProfile(accessToken);
      testResults.user_profile = userProfile;
      console.log('✅ Perfil obtido:', userProfile);
    } catch (error: any) {
      console.error('❌ Erro no perfil:', error.message);
      testResults.errors.push({ test: 'user_profile', error: error.message });
    }

    // === TESTE 2: INSIGHTS DE USUÁRIO - DIFERENTES PERÍODOS ===
    if (testResults.user_profile) {
      const userId = testResults.user_profile.id;
      const periods = ['day', 'week', 'days_28', 'month', 'lifetime'];
      
      for (const period of periods) {
        try {
          console.log(`📊 Teste 2.${periods.indexOf(period) + 1}: Insights do usuário - período ${period}`);
          const insights = await instagramAPI.getUserInsights(userId, accessToken, undefined, period);
          testResults.user_insights[period] = insights;
          console.log(`✅ Insights ${period}:`, insights.summary);
        } catch (error: any) {
          console.error(`❌ Erro insights ${period}:`, error.message);
          testResults.errors.push({ test: `user_insights_${period}`, error: error.message });
        }
      }
    }

    // === TESTE 3: LISTA DE MÍDIAS ===
    try {
      console.log('📱 Teste 3: Obtendo lista de mídias...');
      
      const response = await fetch(
        `https://graph.instagram.com/v23.0/${testResults.user_profile?.id}/media?fields=id,media_type,media_url,permalink,timestamp,caption,like_count,comments_count&limit=25&access_token=${accessToken}`
      );
      
      const mediaData = await response.json();
      testResults.media_list = mediaData;
      console.log(`✅ ${mediaData.data?.length || 0} mídias encontradas`);
      
      if (mediaData.data && mediaData.data.length > 0) {
        console.log('📋 Tipos de mídia encontrados:', mediaData.data.map((m: any) => m.media_type));
      }
    } catch (error: any) {
      console.error('❌ Erro na lista de mídias:', error.message);
      testResults.errors.push({ test: 'media_list', error: error.message });
    }

    // === TESTE 4: INSIGHTS DE MÍDIA (para cada mídia encontrada) ===
    if (testResults.media_list?.data && testResults.media_list.data.length > 0) {
      console.log('🎬 Teste 4: Testando insights de mídias...');
      
      const mediaLimit = Math.min(3, testResults.media_list.data.length); // Testa até 3 mídias
      
      for (let i = 0; i < mediaLimit; i++) {
        const media = testResults.media_list.data[i];
        try {
          console.log(`📊 Teste 4.${i + 1}: Insights da mídia ${media.id} (${media.media_type})`);
          
          const mediaInsights = await instagramAPI.getCompleteMediaInsights(media.id, accessToken);
          testResults.media_insights[media.id] = {
            media_info: media,
            insights: mediaInsights
          };
          
          console.log(`✅ Insights da mídia ${media.id}:`, mediaInsights.insights?.summary);
        } catch (error: any) {
          console.error(`❌ Erro insights mídia ${media.id}:`, error.message);
          testResults.errors.push({ test: `media_insights_${media.id}`, error: error.message });
        }
      }
    }

    // === TESTE 5: MÉTRICAS DISPONÍVEIS - TESTE EXPLORATÓRIO ===
    try {
      console.log('🔬 Teste 5: Explorando métricas disponíveis...');
      
      // Testando diferentes combinações de métricas
      const metricGroups = {
        basic: ['reach', 'profile_views'],
        engagement: ['accounts_engaged', 'follower_count'],
        website: ['website_clicks'],
        all_valid: ['reach', 'profile_views', 'accounts_engaged', 'follower_count', 'website_clicks']
      };

      for (const [groupName, metrics] of Object.entries(metricGroups)) {
        try {
          console.log(`📈 Testando grupo de métricas: ${groupName} - ${metrics.join(', ')}`);
          
          const insights = await instagramAPI.getUserInsights(
            testResults.user_profile!.id, 
            accessToken, 
            metrics, 
            'lifetime'
          );
          
          testResults.available_metrics[groupName] = {
            metrics: metrics,
            result: insights,
            success: true
          };
          
          console.log(`✅ Grupo ${groupName} funcionou:`, insights.summary);
        } catch (error: any) {
          console.error(`❌ Grupo ${groupName} falhou:`, error.message);
          testResults.available_metrics[groupName] = {
            metrics: metrics,
            error: error.message,
            success: false
          };
        }
      }
    } catch (error: any) {
      console.error('❌ Erro no teste de métricas:', error.message);
      testResults.errors.push({ test: 'available_metrics', error: error.message });
    }

    // === TESTE 6: TESTE DE BREAKDOWNS (se tiver mídia) ===
    if (testResults.media_list?.data && testResults.media_list.data.length > 0) {
      try {
        console.log('🔍 Teste 6: Testando breakdowns detalhados...');
        
        const firstMedia = testResults.media_list.data[0];
        const breakdownTests = [
          { metric: 'profile_activity', breakdown: 'action_type' },
          // Outros breakdowns serão testados conforme disponibilidade
        ];

        for (const test of breakdownTests) {
          try {
            console.log(`🔬 Testando breakdown: ${test.metric} por ${test.breakdown}`);
            
            const detailedInsights = await instagramAPI.getDetailedInsights(
              firstMedia.id, 
              accessToken, 
              test.metric, 
              test.breakdown
            );
            
            testResults.available_metrics[`breakdown_${test.metric}_${test.breakdown}`] = {
              media_id: firstMedia.id,
              metric: test.metric,
              breakdown: test.breakdown,
              result: detailedInsights,
              success: true
            };
            
            console.log(`✅ Breakdown ${test.metric}/${test.breakdown} funcionou`);
          } catch (error: any) {
            console.log(`⚠️ Breakdown ${test.metric}/${test.breakdown} não disponível:`, error.message);
            testResults.available_metrics[`breakdown_${test.metric}_${test.breakdown}`] = {
              media_id: firstMedia.id,
              metric: test.metric,
              breakdown: test.breakdown,
              error: error.message,
              success: false
            };
          }
        }
      } catch (error: any) {
        console.error('❌ Erro no teste de breakdowns:', error.message);
        testResults.errors.push({ test: 'breakdowns', error: error.message });
      }
    }

    console.log('🎉 === LABORATÓRIO DE TESTES CONCLUÍDO ===');
    console.log(`✅ Testes executados: ${Object.keys(testResults).length - 2}`); // -2 para errors e timestamp
    console.log(`❌ Erros encontrados: ${testResults.errors.length}`);

    return NextResponse.json({
      success: true,
      message: 'Laboratório de testes Instagram concluído',
      results: testResults,
      summary: {
        user_profile_loaded: !!testResults.user_profile,
        insights_periods_tested: Object.keys(testResults.user_insights).length,
        media_found: testResults.media_list?.data?.length || 0,
        media_insights_tested: Object.keys(testResults.media_insights).length,
        metric_groups_tested: Object.keys(testResults.available_metrics).length,
        total_errors: testResults.errors.length
      }
    });

  } catch (error: any) {
    console.error('❌ Erro fatal no laboratório de testes:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Erro interno no laboratório de testes',
      details: error.message
    }, { status: 500 });
  }
} 