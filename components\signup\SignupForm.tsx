'use client'

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, AlertCircle, Check, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { authService } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';
import type { SignUpFormData } from '@/types/auth';

interface FormData {
  fullName: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
}

interface FormErrors {
  fullName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  acceptTerms?: string;
}

interface PasswordRequirement {
  met: boolean;
  text: string;
}

export const SignupForm: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [signupSuccess, setSignupSuccess] = useState(false);
  const { toast } = useToast();

  // Validação de requisitos da senha
  const getPasswordRequirements = (password: string): PasswordRequirement[] => [
    { met: password.length >= 12, text: 'Pelo menos 12 caracteres' },
    { met: /[A-Z]/.test(password), text: 'Uma letra maiúscula' },
    { met: /[a-z]/.test(password), text: 'Uma letra minúscula' },
    { met: /[!@#$%^&*(),.?":{}|<>]/.test(password), text: 'Um caractere especial' },
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Nome completo é obrigatório';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Nome deve ter pelo menos 2 caracteres';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'E-mail é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'E-mail deve ter um formato válido';
    }

    if (!formData.password) {
      newErrors.password = 'Senha é obrigatória';
    } else {
      const requirements = getPasswordRequirements(formData.password);
      const unmetRequirements = requirements.filter(req => !req.met);
      if (unmetRequirements.length > 0) {
        newErrors.password = 'A senha não atende a todos os requisitos';
      }
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Confirmação de senha é obrigatória';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'As senhas não coincidem';
    }

    if (!formData.acceptTerms) {
      newErrors.acceptTerms = 'Você deve aceitar os termos de serviço';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Erro na validação",
        description: "Por favor, corrija os erros no formulário antes de continuar.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const signupData: SignUpFormData = {
        email: formData.email.trim(),
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        fullName: formData.fullName.trim(),
        acceptTerms: formData.acceptTerms,
      };

      const result = await authService.signUp(signupData);
      
      if (result.success) {
        setSignupSuccess(true);
        toast({
          title: "Conta criada com sucesso!",
          description: result.message,
          variant: "default",
        });
        
        // Limpar formulário
        setFormData({
          fullName: '',
          email: '',
          password: '',
          confirmPassword: '',
          acceptTerms: false,
        });
      } else {
        toast({
          title: "Erro ao criar conta",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Erro no cadastro:', error);
      toast({
        title: "Erro inesperado",
        description: "Ocorreu um erro durante o cadastro. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormBasicallyValid = () => {
    return formData.fullName.trim() && 
           formData.email.trim() && 
           formData.password.length >= 6 &&
           formData.confirmPassword.length >= 6 &&
           formData.acceptTerms;
  };

  const passwordRequirements = getPasswordRequirements(formData.password);
  const allPasswordRequirementsMet = passwordRequirements.every(req => req.met);

  // Se o cadastro foi bem-sucedido, mostrar mensagem de sucesso
  if (signupSuccess) {
    return (
      <div className="w-full  mx-auto space-y-6">
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Conta criada com sucesso!</strong>
            <br />
            Verifique seu email para confirmar sua conta e fazer login.
          </AlertDescription>
        </Alert>
        
        <Button
          onClick={() => setSignupSuccess(false)}
          variant="outline"
          className="w-full"
        >
          Criar outra conta
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full  mx-auto space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Campo Nome Completo */}
        <div className="space-y-2">
          <Label htmlFor="fullName" className="text-sm font-medium text-foreground">
            Nome completo
          </Label>
          <Input
            id="fullName"
            type="text"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            placeholder="Digite seu nome completo"
            className={cn(
              "h-11 transition-colors",
              errors.fullName && "border-destructive focus-visible:ring-destructive"
            )}
          />
          {errors.fullName && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{errors.fullName}</span>
            </div>
          )}
        </div>

        {/* Campo E-mail */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium text-foreground">
            E-mail comercial
          </Label>
          <p className="text-xs text-muted-foreground">
            Você precisará confirmar este endereço de e-mail.
          </p>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="Digite seu e-mail comercial"
            className={cn(
              "h-11 transition-colors",
              errors.email && "border-destructive focus-visible:ring-destructive"
            )}
          />
          {errors.email && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{errors.email}</span>
            </div>
          )}
        </div>

        {/* Campo Senha */}
        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium text-foreground">
            Senha
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="Digite sua senha"
              className={cn(
                "h-11 pr-10 transition-colors",
                errors.password && "border-destructive focus-visible:ring-destructive"
              )}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label={showPassword ? 'Ocultar senha' : 'Mostrar senha'}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
          
          {/* Requisitos da senha */}
          {formData.password && (
            <Card className="mt-3">
              <CardContent className="p-3">
                <p className="text-sm font-medium mb-2">Requisitos da senha:</p>
                <div className="space-y-1">
                  {passwordRequirements.map((requirement, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <div className={cn(
                        "flex items-center justify-center w-4 h-4 rounded-full",
                        requirement.met 
                          ? "bg-green-100 text-green-600" 
                          : "bg-muted text-muted-foreground"
                      )}>
                        {requirement.met && <Check className="h-3 w-3" />}
                      </div>
                      <span className={cn(
                        requirement.met ? "text-green-600" : "text-muted-foreground"
                      )}>
                        {requirement.text}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {errors.password && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{errors.password}</span>
            </div>
          )}
        </div>

        {/* Campo Confirmar Senha */}
        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-sm font-medium text-foreground">
            Confirmar senha
          </Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              placeholder="Confirme sua senha"
              className={cn(
                "h-11 pr-10 transition-colors",
                errors.confirmPassword && "border-destructive focus-visible:ring-destructive",
                formData.password && formData.confirmPassword && formData.password === formData.confirmPassword && "border-green-500 focus-visible:ring-green-500"
              )}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label={showConfirmPassword ? 'Ocultar senha' : 'Mostrar senha'}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>

          {formData.password && formData.confirmPassword && formData.password === formData.confirmPassword && (
            <div className="flex items-center gap-2 text-sm text-green-600">
              <Check className="h-4 w-4" />
              <span>As senhas coincidem</span>
            </div>
          )}

          {errors.confirmPassword && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{errors.confirmPassword}</span>
            </div>
          )}
        </div>

        {/* Checkbox Termos */}
        <div className="space-y-2">
          <div className="flex items-start gap-3">
            <input
              id="acceptTerms"
              type="checkbox"
              checked={formData.acceptTerms}
              onChange={(e) => handleInputChange('acceptTerms', e.target.checked)}
              className="mt-1 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <Label htmlFor="acceptTerms" className="text-sm text-muted-foreground leading-5 cursor-pointer">
              Ao criar uma conta, concordo com os{' '}
              <a href="/legal-terms" className="text-primary font-medium hover:underline">
                Termos da Aface
              </a>
              , incluindo os termos de pagamento e{' '}
              <a href="/privacy-policy" className="text-primary font-medium hover:underline">
                Política de Privacidade
              </a>
            </Label>
          </div>
          {errors.acceptTerms && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{errors.acceptTerms}</span>
            </div>
          )}
        </div>

        {/* Botão de envio */}
        <Button
          type="submit"
          disabled={!isFormBasicallyValid() || isSubmitting}
          className={cn(
            "w-full h-11 text-base font-semibold transition-all duration-200",
            "bg-[#fa12e3] hover:bg-[#E63E38] active:scale-[0.98]",
            "disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed"
          )}
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              Criando conta...
            </div>
          ) : (
            'Criar minha conta'
          )}
        </Button>
      </form>
    </div>
  );
};
