'use client'

import React, { useState } from 'react';

export const LanguageSelector: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('pt-BR');

  const languages = [
    { code: 'pt-BR', name: '<PERSON>ug<PERSON><PERSON><PERSON> (Brasil)' },
    { code: 'en-US', name: 'English (US)' },
    { code: 'es-ES', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { code: 'fr-FR', name: 'Français' },
  ];

  const handleLanguageSelect = (languageCode: string) => {
    setSelectedLanguage(languageCode);
    setIsOpen(false);
  };

  return (
      <div className="box-border flex w-20 h-12 justify-center items-start gap-2 absolute px-4 py-3.5 rounded-lg left-16 top-[1115px]">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 hover:opacity-80 transition-opacity"
        aria-label="Seletor de idioma"
      >
        <svg width="20" height="20" viewBox="0 0 21 21" fill="white" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.1855 20.6067C4.68555 20.6067 0.185547 16.1067 0.185547 10.6067C0.185547 5.10669 4.68555 0.606689 10.1855 0.606689C15.6855 0.606689 20.1855 5.10669 20.1855 10.6067C20.1855 16.1067 15.6855 20.6067 10.1855 20.6067ZM10.1855 3.10669C6.05555 3.10669 2.68555 6.47669 2.68555 10.6067C2.68555 14.7267 6.05555 18.1067 10.1855 18.1067C14.3055 18.1067 17.6855 14.7267 17.6855 10.6067C17.6855 6.47669 14.3055 3.10669 10.1855 3.10669ZM5.43555 10.6067C5.80555 9.60669 7.43555 9.35669 8.18555 9.72669C8.93555 10.1067 9.55555 9.72669 10.3055 10.6067C10.9355 11.4767 11.5555 11.2267 11.6855 12.4767C11.8055 13.7267 10.0555 15.9767 9.68555 16.6067C9.18555 17.2267 7.93555 16.4767 7.93555 14.7267C7.93555 12.9767 7.05555 12.8567 6.55555 12.7267C5.93555 12.4767 4.68555 12.3567 5.43555 10.6067ZM8.55555 4.97669C9.80555 4.35669 10.6855 4.97669 11.6855 4.97669C12.1855 4.97669 12.6855 4.97669 13.1855 5.10669C15.0555 6.22669 16.4355 8.22669 16.4355 10.6067C16.4355 11.1067 16.3055 11.6067 16.1855 12.1067C15.8055 11.9767 15.1855 10.7267 14.4355 10.8567C13.5555 10.9767 13.4355 9.35669 11.9355 9.72669C10.8055 9.97669 10.3055 9.60669 10.4355 8.97669C10.5555 8.22669 11.1855 7.60669 10.6855 6.97669C10.1855 6.35669 10.0555 5.97669 8.80555 5.72669C8.30555 5.60669 8.55555 4.97669 8.55555 4.97669Z" fill="white"/>
        </svg>
        <svg width="20" height="24" viewBox="0 0 21 21" fill="white" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.1855 13.4399L5.18555 8.43994L6.35221 7.27327L10.1855 11.1066L14.0189 7.27327L15.1855 8.43994L10.1855 13.4399Z" fill="white"/>
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-12 left-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-48">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageSelect(language.code)}
              className={`w-full text-left px-4 py-2 bg-white hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg ${
                selectedLanguage === language.code ? 'bg-gray-50 font-medium' : ''
              }`}
            >
              {language.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
