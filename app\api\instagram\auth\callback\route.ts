import { NextRequest, NextResponse } from 'next/server';
import { instagramAPI } from '@/lib/instagram-api';

/**
 * Rota de callback após autorização do Instagram
 * GET /api/instagram/auth/callback
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const error_description = searchParams.get('error_description');

    // Verifica se houve erro na autorização
    if (error) {
      console.error('❌ Erro na autorização:', error, error_description);
      return NextResponse.json({
        success: false,
        error: error,
        message: error_description || 'Usuário negou as permissões'
      }, { status: 400 });
    }

    if (!code) {
      console.error('❌ Código de autorização não fornecido');
      return NextResponse.json({
        success: false,
        error: 'missing_code',
        message: 'Código de autorização não fornecido'
      }, { status: 400 });
    }

    console.log('🔍 Processando callback do Instagram');
    console.log('🔍 State recebido:', state);
    console.log('🔍 Code recebido:', code ? code.substring(0, 10) + '...' : 'NÃO FORNECIDO');

    // Troca o código pelo token de acesso
    console.log('🔄 Trocando código por token...');
    const tokenData = await instagramAPI.exchangeCodeForToken(code);

    if (!tokenData.access_token) {
      throw new Error('Token de acesso não foi retornado pela API do Instagram');
    }

    // Converte para token de longa duração (60 dias)
    console.log('🔄 Convertendo para token de longa duração...');
    const longLivedTokenData = await instagramAPI.exchangeForLongLivedToken(tokenData.access_token);

    // Obtém informações do perfil do usuário
    console.log('👤 Obtendo informações do perfil...');
    const userProfile = await instagramAPI.getUserProfile(longLivedTokenData.access_token);

    // Dados finais da autenticação
    const authResult = {
      user: {
        id: userProfile.id,
        username: userProfile.username,
        name: userProfile.name || userProfile.username,
        profile_picture_url: userProfile.profile_picture_url,
        followers_count: userProfile.followers_count,
        media_count: userProfile.media_count,
        account_type: userProfile.account_type
      },
      tokens: {
        access_token: longLivedTokenData.access_token,
        token_type: longLivedTokenData.token_type || 'bearer',
        expires_in: longLivedTokenData.expires_in || 5183999 // 60 dias em segundos
      },
      scopes: [
        'instagram_business_basic',
        'instagram_business_content_publish',
        'instagram_business_manage_messages',
        'instagram_business_manage_comments',
        'instagram_business_manage_insights'
      ],
      authenticated_at: new Date().toISOString()
    };

    console.log('✅ Autenticação Instagram bem-sucedida');
    console.log('👤 Usuário autenticado:', {
      id: userProfile.id,
      username: userProfile.username,
      account_type: userProfile.account_type
    });

    // Em uma aplicação real, você salvaria essas informações no banco de dados
    // e redirecionaria o usuário para o dashboard
    
    // Por enquanto, vamos retornar os dados e redirecionar para o dashboard
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.INSTAGRAM_REDIRECT_URI?.replace('/api/instagram/auth/callback', '') || 'http://localhost:3000';
    const dashboardUrl = new URL('/dashboard', baseUrl);
    dashboardUrl.searchParams.set('instagram_auth', 'success');
    dashboardUrl.searchParams.set('instagram_user', userProfile.username);
    dashboardUrl.searchParams.set('instagram_data', encodeURIComponent(JSON.stringify(authResult)));

    console.log('🔗 Redirecionando para:', dashboardUrl.toString());
    return NextResponse.redirect(dashboardUrl);

  } catch (error: unknown) {
    console.error('❌ Erro no callback do Instagram:', error);
    
    // Em caso de erro, redireciona para o dashboard com erro
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.INSTAGRAM_REDIRECT_URI?.replace('/api/instagram/auth/callback', '') || 'http://localhost:3000';
    const dashboardUrl = new URL('/dashboard', baseUrl);
    dashboardUrl.searchParams.set('instagram_auth', 'error');
    dashboardUrl.searchParams.set('error_message', encodeURIComponent(error instanceof Error ? error.message : 'Erro desconhecido'));

    console.log('❌ Erro - Redirecionando para:', dashboardUrl.toString());
    return NextResponse.redirect(dashboardUrl);
  }
} 