"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { useTikTokAuth } from "@/hooks/use-tiktok-auth";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Shield, 
  Users, 
  Heart, 
  Video, 
  BarChart3,
  AlertTriangle,
  ExternalLink,
  RefreshCw
} from "lucide-react";

/**
 * Componente de Status de Conexão do TikTok
 * 
 * Exibe informações sobre a autenticação e conexão do usuário com o TikTok,
 * incluindo dados do perfil, estatísticas e status de segurança.
 * 
 * Funcionalidades:
 * - Status visual da conexão
 * - Informações detalhadas do perfil
 * - Estatísticas de engagement
 * - Controles de autenticação
 * - Indicadores de segurança
 * - Suporte a modo claro/escuro
 */
export function TikTokConnectionStatus() {
  const { 
    isAuthenticated, 
    user, 
    isLoading, 
    error, 
    authStatus, 
    lastAuthAttempt,
    logout, 
    clearError,
    refreshAuth 
  } = useTikTokAuth();

  // Estado local para controlar popup
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  /**
   * Abre popup de autenticação do TikTok
   * Melhora a UX mantendo o usuário na aplicação principal
   */
  const handleTikTokAuth = async () => {
    try {
      setIsPopupOpen(true);
      console.log('🚀 Iniciando autenticação TikTok via popup...');
      
      // Primeiro obtém a URL de autenticação (com parâmetro popup)
      const response = await fetch('/api/tiktok/auth/login?popup=true');
      const data = await response.json();
      
      if (!response.ok || !data.authUrl) {
        throw new Error(data.error || 'Erro ao obter URL de autenticação');
      }

      // Configurações do popup
      const popupWidth = 500;
      const popupHeight = 700;
      const left = (window.screen.width - popupWidth) / 2;
      const top = (window.screen.height - popupHeight) / 2;
      
      const popupOptions = [
        `width=${popupWidth}`,
        `height=${popupHeight}`,
        `left=${left}`,
        `top=${top}`,
        'scrollbars=yes',
        'resizable=yes',
        'status=no',
        'location=no',
        'toolbar=no',
        'menubar=no'
      ].join(',');

      // Abre o popup
      console.log('🪟 Abrindo popup de autenticação...');
      const popup = window.open(data.authUrl, 'tiktok_auth', popupOptions);
      
      if (!popup) {
        // Fallback: redireciona se popup foi bloqueado
        setIsPopupOpen(false);
        console.warn('🚫 Popup bloqueado pelo navegador. Usando redirecionamento tradicional...');
        
        // Mostra mensagem informativa
        alert('⚠️ Popup bloqueado!\n\nVamos abrir a autenticação TikTok na mesma janela.\nVocê será redirecionado de volta após a autorização.');
        
        // Faz requisição sem popup=true para ter redirecionamento
        window.location.href = '/api/tiktok/auth/login';
        return;
      }

      // Escuta mensagens do popup
      const handleMessage = (event: MessageEvent) => {
        // Verificação de segurança da origem
        if (event.origin !== window.location.origin) {
          return;
        }

        const { type, success, error: authError, user: userData } = event.data;

        if (type === 'TIKTOK_AUTH_RESULT') {
          // Remove o listener e reseta estado
          window.removeEventListener('message', handleMessage);
          setIsPopupOpen(false);
          
          // Fecha o popup
          if (popup && !popup.closed) {
            popup.close();
          }

          if (success) {
            console.log('✅ Autenticação TikTok realizada com sucesso via popup!');
            // O hook já deve detectar a mudança automaticamente
            refreshAuth();
          } else {
            console.error('❌ Erro na autenticação TikTok:', authError);
          }
        }
      };

      // Adiciona listener para mensagens
      window.addEventListener('message', handleMessage);

      // Monitora se o popup foi fechado manualmente
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          setIsPopupOpen(false);
          console.log('🔄 Popup de autenticação TikTok foi fechado');
        }
      }, 1000);

      // Cleanup após 5 minutos (timeout)
      setTimeout(() => {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
        setIsPopupOpen(false);
        if (popup && !popup.closed) {
          popup.close();
        }
      }, 5 * 60 * 1000);

    } catch (error) {
      setIsPopupOpen(false);
      console.error('❌ Erro ao iniciar autenticação TikTok:', error);
      
      // Mostra mensagem de erro e usa fallback
      console.warn('🔄 Usando método de redirecionamento como fallback...');
      alert('⚠️ Erro no popup!\n\nVamos usar o método tradicional de autenticação.\nVocê será redirecionado para o TikTok.');
      
      // Fallback: redireciona sem popup
      window.location.href = '/api/tiktok/auth/login';
    }
  };

  // Estado de carregamento
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center">
              <RefreshCw className="w-5 h-5 text-white animate-spin" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">TikTok</CardTitle>
              <CardDescription>Verificando conexão...</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span className="text-sm">Carregando dados de autenticação...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Estado de erro
  if (error) {
    return (
      <Card className="w-full border-destructive/50">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-destructive rounded-lg flex items-center justify-center">
              <XCircle className="w-5 h-5 text-destructive-foreground" />
            </div>
            <div className="flex-1">
              <CardTitle className="text-lg font-semibold text-destructive">
                Erro na Conexão TikTok
              </CardTitle>
              <CardDescription>Falha na autenticação</CardDescription>
            </div>
            <Badge variant="destructive" className="gap-1">
              <AlertTriangle className="w-3 h-3" />
              Erro
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-3 bg-destructive/10 rounded-lg border border-destructive/20">
            <p className="text-sm text-destructive font-medium mb-2">Detalhes do erro:</p>
            <p className="text-sm text-muted-foreground">{error}</p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearError}
              className="flex-1"
            >
              Limpar Erro
            </Button>
            <Button 
              variant="default" 
              size="sm" 
              onClick={() => window.location.reload()}
              className="flex-1"
            >
              Tentar Novamente
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Estado conectado
  if (isAuthenticated && user) {
    return (
      <Card className="w-full border-emerald-200 dark:border-emerald-800">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg font-semibold">TikTok</CardTitle>
              </div>
              <CardDescription>Conectado com sucesso</CardDescription>
            </div>
            <Badge variant="default" className="gap-1 bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300">
              <CheckCircle className="w-3 h-3" />
              Ativo
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Informações do Perfil */}
          <div className="flex items-center gap-4">
            <Avatar className="w-16 h-16">
              <AvatarImage 
                src={user.avatar_url_100 || user.avatar_url} 
                alt={user.display_name}
              />
              <AvatarFallback className="bg-gradient-to-br from-pink-500 to-purple-600 text-white font-semibold">
                {user.display_name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-lg truncate">{user.display_name}</h3>
                {user.is_verified && (
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-3 h-3 text-white" />
                  </div>
                )}
              </div>
              
              {user.username && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                  <span>@{user.username}</span>
                </div>
              )}
              
              {user.bio_description && (
                <div className="text-xs text-muted-foreground mb-1 line-clamp-2">
                  {user.bio_description}
                </div>
              )}
              
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <span>ID:</span>
                <code className="bg-muted px-1 py-0.5 rounded text-xs">
                  {user.open_id.substring(0, 12)}...
                </code>
              </div>
            </div>
          </div>

          <Separator />

          {/* Estatísticas do Usuário */}
          {(user.follower_count !== undefined || user.following_count !== undefined || 
            user.likes_count !== undefined || user.video_count !== undefined) && (
            <>
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <BarChart3 className="w-4 h-4 text-purple-600" />
                  <h4 className="text-sm font-medium">Estatísticas do Perfil</h4>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  {user.follower_count !== undefined && (
                    <div className="p-3 bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-950/30 dark:to-purple-950/30 rounded-lg border border-pink-200 dark:border-pink-800">
                      <div className="flex items-center gap-2 mb-1">
                        <Users className="w-4 h-4 text-pink-600" />
                        <span className="text-xs font-medium text-pink-700 dark:text-pink-300">Seguidores</span>
                      </div>
                      <div className="text-lg font-bold text-pink-700 dark:text-pink-300">
                        {user.follower_count.toLocaleString('pt-BR')}
                      </div>
                    </div>
                  )}
                  
                  {user.following_count !== undefined && (
                    <div className="p-3 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center gap-2 mb-1">
                        <Users className="w-4 h-4 text-blue-600" />
                        <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Seguindo</span>
                      </div>
                      <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
                        {user.following_count.toLocaleString('pt-BR')}
                      </div>
                    </div>
                  )}
                  
                  {user.likes_count !== undefined && (
                    <div className="p-3 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30 rounded-lg border border-red-200 dark:border-red-800">
                      <div className="flex items-center gap-2 mb-1">
                        <Heart className="w-4 h-4 text-red-600" />
                        <span className="text-xs font-medium text-red-700 dark:text-red-300">Curtidas</span>
                      </div>
                      <div className="text-lg font-bold text-red-700 dark:text-red-300">
                        {user.likes_count.toLocaleString('pt-BR')}
                      </div>
                    </div>
                  )}
                  
                  {user.video_count !== undefined && (
                    <div className="p-3 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/30 dark:to-indigo-950/30 rounded-lg border border-purple-200 dark:border-purple-800">
                      <div className="flex items-center gap-2 mb-1">
                        <Video className="w-4 h-4 text-purple-600" />
                        <span className="text-xs font-medium text-purple-700 dark:text-purple-300">Vídeos</span>
                      </div>
                      <div className="text-lg font-bold text-purple-700 dark:text-purple-300">
                        {user.video_count.toLocaleString('pt-BR')}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <Separator />
            </>
          )}

          {/* Informações Básicas */}
          <div className="p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                Dados Conectados
              </span>
            </div>
            <div className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
              <div>✅ Perfil básico: Avatar, Nome, ID único</div>
              <div>✅ Estatísticas: Seguidores, Curtidas, Vídeos</div>
              {(!user.follower_count && !user.likes_count) && (
                <div>⚠️ Algumas estatísticas podem não estar disponíveis</div>
              )}
            </div>
          </div>

          {/* Informações de Conexão */}
          <div className="p-3 bg-emerald-50 dark:bg-emerald-950/30 rounded-lg border border-emerald-200 dark:border-emerald-800">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="w-4 h-4 text-emerald-600" />
              <span className="text-sm font-medium text-emerald-700 dark:text-emerald-300">
                Conexão Segura Ativa
              </span>
            </div>
            <div className="text-xs text-emerald-600 dark:text-emerald-400 space-y-1">
              {lastAuthAttempt && (
                <div>Conectado em: {new Date(lastAuthAttempt).toLocaleString('pt-BR')}</div>
              )}
              <div>Protocolo: OAuth 2.0 com proteção CSRF</div>
              <div>Status: Autenticado e autorizado</div>
            </div>
          </div>

          {/* Controles */}
          <div className="flex flex-col sm:flex-row gap-2">
            {user.profile_deep_link && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => window.open(user.profile_deep_link, '_blank')}
                className="flex-1 gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Ver no TikTok
              </Button>
            )}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshAuth}
              className="flex-1 gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Atualizar Dados
            </Button>
            <Button 
              variant="destructive" 
              size="sm" 
              onClick={logout}
              className="flex-1"
            >
              Desconectar
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Estado desconectado
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center">
            <XCircle className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold">TikTok</CardTitle>
            <CardDescription>Não conectado</CardDescription>
          </div>
          <Badge variant="secondary" className="gap-1">
            <Clock className="w-3 h-3" />
            Inativo
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="p-4 bg-muted/50 rounded-lg text-center">
          <BarChart3 className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm font-medium mb-1">Conecte sua conta TikTok</p>
          <p className="text-xs text-muted-foreground">
            Obtenha insights detalhados, gerencie conteúdo e monitore performance
          </p>
        </div>

        <div className="space-y-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
            <span>Análise de performance de vídeos</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
            <span>Estatísticas de engajamento</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
            <span>Dados de audiência</span>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="w-3 h-3 text-blue-500 flex-shrink-0" />
            <span>Conexão segura com OAuth 2.0</span>
          </div>
        </div>

        <Button 
          className="w-full" 
          onClick={() => {
            handleTikTokAuth();
          }}
          disabled={isLoading || isPopupOpen}
        >
          {isLoading ? 'Carregando...' : 
           isPopupOpen ? 'Aguardando TikTok...' : 
           'Conectar TikTok'}
        </Button>
      </CardContent>
    </Card>
  );
} 