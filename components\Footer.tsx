import React from 'react';
import Link from 'next/link';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-black text-white pt-[82px] pb-[93px] px-5">
      <div className="max-w-[1241px] mx-auto">
        <div className="mb-[93px]">
          <Link href="/" className="block w-[183px]">
            <img
              src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/06eb56a5243409963f7548ed87b6c68748f9a78f?placeholderIfAbsent=true"
              alt="Metricool"
              className="aspect-[4.57] object-contain w-full h-10"
            />
          </Link>
          
          <div className="flex items-center space-x-5 mt-7">
            <a href="#" className="w-8 h-8 hover:opacity-80 transition-opacity">
              <img
                src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/bae9340495bcf176e60e9374c4b4e558998d0d6a?placeholderIfAbsent=true"
                alt="Facebook"
                className="w-full h-full object-contain"
              />
            </a>
            <a href="#" className="w-8 h-8 hover:opacity-80 transition-opacity">
              <img
                src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/9a47c83c3893816579d8dabb1f9f679ab529f5f4?placeholderIfAbsent=true"
                alt="Twitter/X"
                className="w-full h-full object-contain"
              />
            </a>
            <a href="#" className="w-8 h-8 hover:opacity-80 transition-opacity">
              <img
                src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/bb45dd55e18b107ad1d1982a5ab2f93b96323370?placeholderIfAbsent=true"
                alt="Instagram"
                className="w-full h-full object-contain"
              />
            </a>
            <a href="#" className="w-8 h-8 hover:opacity-80 transition-opacity">
              <img
                src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/61a4458e7917b171bd913f04a171630208d95faf?placeholderIfAbsent=true"
                alt="TikTok"
                className="w-full h-full object-contain"
              />
            </a>
            <a href="#" className="w-8 h-8 hover:opacity-80 transition-opacity">
              <img
                src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/950108e288dd7aa6e4d7dbe0590461dac62ad826?placeholderIfAbsent=true"
                alt="YouTube"
                className="w-full h-full object-contain"
              />
            </a>
            <a href="#" className="w-8 h-8 hover:opacity-80 transition-opacity">
              <img
                src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/6b34080f168d005463638cf0db0f35cd59fe90ff?placeholderIfAbsent=true"
                alt="LinkedIn"
                className="w-full h-full object-contain"
              />
            </a>
            <a href="#" className="w-8 h-8 hover:opacity-80 transition-opacity">
              <img
                src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/b1e99fc14686ef0574e502d75ee3f078c0c5076d?placeholderIfAbsent=true"
                alt="Pinterest"
                className="w-full h-full object-contain"
              />
            </a>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-[25px] mb-20">
          <div className="text-base leading-[1.3]">
            <h3 className="font-extrabold mb-[30px]">Company</h3>
            <ul className="space-y-[31px] font-normal">
              <li><a href="#" className="hover:text-gray-300 transition-colors">Contact</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Affiliate Program</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Reviews</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Careers</a></li>
            </ul>
          </div>

          <div className="text-base leading-[1.3]">
            <h3 className="font-extrabold mb-[30px]">Product</h3>
            <ul className="space-y-[31px] font-normal">
              <li><a href="#" className="hover:text-gray-300 transition-colors">Schedule Instagram</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Best time to post</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Schedule Twitter threads</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Schedule Instagram carousel</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Chrome Extension</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Integrations</a></li>
            </ul>
          </div>

          <div className="text-base leading-[1.3]">
            <h3 className="font-extrabold mb-[30px]">Resources</h3>
            <ul className="space-y-[30px] font-normal">
              <li><a href="#" className="hover:text-gray-300 transition-colors">Newsroom</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Instagram engagement rate<br />calculator</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Integrations</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">White Label</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Autolists</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Resource center</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Newsletter</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">Comparison</a></li>
            </ul>
          </div>

          <div className="text-base leading-[1.3]">
            <h3 className="font-extrabold mb-[30px]">Studies</h3>
            <ul className="space-y-[31px] font-normal">
              <li><a href="#" className="hover:text-gray-300 transition-colors">2025 Social Media Study</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">2025 LinkedIn Study</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">2025 Social Ads Study</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">2024 X/Twitter Study</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">2024 Short-Form Video Study</a></li>
              <li><a href="#" className="hover:text-gray-300 transition-colors">2024 Instagram Study</a></li>
            </ul>
          </div>

          <div className="pt-20">
            <div className="space-y-5">
              <a href="#" className="block">
                <img
                  src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/4103e6e924dfcd6db2b4f7f203756e10cfba44f6?placeholderIfAbsent=true"
                  alt="Download on the App Store"
                  className="w-[150px] h-11 object-contain"
                />
              </a>
              <a href="#" className="block">
                <img
                  src="https://api.builder.io/api/v1/image/assets/08d568287fd440c0b248357748dfef0a/e8b93e2bd7f54c97a304bf7b07ada0552fa690f5?placeholderIfAbsent=true"
                  alt="Get it on Google Play"
                  className="w-[150px] h-11 object-contain"
                />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-600 pt-20">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-8 text-base leading-[1.3]">
            <div className="font-normal">
              Copyright © 2025 Metricool
            </div>
            
            <nav className="font-normal">
              <ul className="flex flex-wrap gap-5">
                <li><a href="#" className="hover:text-gray-300 transition-colors">Terms & Conditions</a></li>
                <li><a href="#" className="hover:text-gray-300 transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-gray-300 transition-colors">Cookies Policy</a></li>
                <li><a href="#" className="hover:text-gray-300 transition-colors">Legal Notice</a></li>
                <li><a href="#" className="hover:text-gray-300 transition-colors">Online Dispute Resolution</a></li>
                <li><a href="#" className="hover:text-gray-300 transition-colors">Whistleblower Channel</a></li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </footer>
  );
};
