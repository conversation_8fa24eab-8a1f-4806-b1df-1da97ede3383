'use client'

import { Suspense } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { InstagramConnectionStatus } from '@/components/instagram/instagram-connection-status';
import { InstagramInsightsDashboard } from '@/components/instagram/instagram-insights-dashboard';
import { InstagramTestLab } from '@/components/instagram/instagram-test-lab';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RiBarChartBoxLine, 
  RiTeamLine, 
  RiSecurePaymentLine, 
  RiLineChartLine,
  RiEyeLine,
  RiArrowRightLine,
  RiArrowUpLine 
} from '@remixicon/react';

// Componente de Loading para o Instagram
function InstagramLoadingSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Skeleton className="w-8 h-8 rounded-lg" />
          <div>
            <Skeleton className="h-5 w-32 mb-1" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-5 w-24" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
        <Skeleton className="h-16 w-full rounded-lg" />
        <Skeleton className="h-8 w-32" />
      </CardContent>
    </Card>
  );
}

// Componente de Loading para Insights
function InsightsLoadingSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-16 w-full rounded-lg" />
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Skeleton key={i} className="h-24 w-full rounded-lg" />
        ))}
      </div>
    </div>
  );
}

export default function Dashboard() {
  const { user } = useAuth();

  const stats = [
    {
      title: "Vendas Hoje",
      value: "R$ 12.347",
      change: "+12,5%",
      changeType: "positive" as const,
      icon: RiBarChartBoxLine,
      description: "Comparado a ontem"
    },
    {
      title: "Novos Clientes",
      value: "34",
      change: "+8,2%",
      changeType: "positive" as const,
      icon: RiTeamLine,
      description: "Últimos 7 dias"
    },
    {
      title: "Pagamentos",
      value: "R$ 98.750",
      change: "+23,1%",
      changeType: "positive" as const,
      icon: RiSecurePaymentLine,
      description: "Este mês"
    },
    {
      title: "Taxa Conversão",
      value: "3,24%",
      change: "+0,4%",
      changeType: "positive" as const,
      icon: RiLineChartLine,
      description: "Média mensal"
    }
  ];

  return (
    <div className="space-y-6 p-6 w-full">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Bem-vindo de volta, {user?.fullName || 'Usuário'}! Acompanhe suas métricas e conexões.
        </p>
      </div>

      {/* Tabs para organizar conteúdo */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="instagram">Instagram</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="tests">Testes</TabsTrigger>
        </TabsList>

        {/* Tab de Visão Geral */}
        <TabsContent value="overview" className="space-y-6">
          {/* Grid de métricas principais */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <stat.icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <span className={`flex items-center gap-1 ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      <RiArrowUpLine className="h-3 w-3" />
                      {stat.change}
                    </span>
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Conexão Instagram na visão geral */}
          <Suspense fallback={<InstagramLoadingSkeleton />}>
            <InstagramConnectionStatus />
          </Suspense>
        </TabsContent>

        {/* Tab do Instagram */}
        <TabsContent value="instagram" className="space-y-6">
          <Suspense fallback={<InstagramLoadingSkeleton />}>
            <InstagramConnectionStatus />
          </Suspense>
        </TabsContent>

        {/* Tab de Insights */}
        <TabsContent value="insights" className="space-y-6">
          <Suspense fallback={<InsightsLoadingSkeleton />}>
            <InstagramInsightsDashboard />
          </Suspense>
        </TabsContent>

        {/* Tab de Testes */}
        <TabsContent value="tests" className="space-y-6">
          <Suspense fallback={<InsightsLoadingSkeleton />}>
            <InstagramTestLab />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
} 