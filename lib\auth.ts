import { supabase } from './supabase';
import type { SignUpFormData, SignInFormData, AuthResponse, AuthUser } from '@/types/auth';

class AuthService {
  private getRedirectUrl(): string {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    return process.env.NEXT_PUBLIC_REDIRECT_URL || `${baseUrl}/auth/callback`;
  }

  // Cadastro de novo usuário
  async signUp(data: SignUpFormData): Promise<AuthResponse> {
    try {
      const { email, password, fullName } = data;

      // Criar usuário no Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            display_name: fullName,
          },
          emailRedirectTo: this.getRedirectUrl(),
        },
      });

      if (authError) {
        return {
          success: false,
          error: this.translateError(authError.message),
        };
      }

      if (!authData.user) {
        return {
          success: false,
          error: 'Erro ao criar usuário',
        };
      }

      return {
        success: true,
        user: {
          id: authData.user.id,
          email: authData.user.email!,
          fullName: authData.user.user_metadata?.full_name,
          emailConfirmed: authData.user.email_confirmed_at ? true : false,
          createdAt: authData.user.created_at,
        },
        message: authData.user.email_confirmed_at 
          ? 'Conta criada com sucesso!' 
          : 'Conta criada com sucesso! Verifique seu email para confirmar.',
      };
    } catch (error) {
      console.error('Erro no signup:', error);
      return {
        success: false,
        error: 'Erro interno do servidor',
      };
    }
  }

  // Login do usuário
  async signIn(data: SignInFormData): Promise<AuthResponse> {
    try {
      const { email, password } = data;

      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) {
        return {
          success: false,
          error: this.translateError(authError.message),
        };
      }

      if (!authData.user) {
        return {
          success: false,
          error: 'Usuário não encontrado',
        };
      }

      return {
        success: true,
        user: {
          id: authData.user.id,
          email: authData.user.email!,
          fullName: authData.user.user_metadata?.full_name,
          emailConfirmed: authData.user.email_confirmed_at ? true : false,
          createdAt: authData.user.created_at,
        },
        message: 'Login realizado com sucesso!',
      };
    } catch (error) {
      console.error('Erro no signin:', error);
      return {
        success: false,
        error: 'Erro interno do servidor',
      };
    }
  }

  // Logout
  async signOut(): Promise<AuthResponse> {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        return {
          success: false,
          error: this.translateError(error.message),
        };
      }

      return {
        success: true,
        message: 'Logout realizado com sucesso!',
      };
    } catch (error) {
      console.error('Erro no signout:', error);
      return {
        success: false,
        error: 'Erro interno do servidor',
      };
    }
  }

  // Login com Google
  async signInWithGoogle(): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: this.getRedirectUrl(),
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        return {
          success: false,
          error: this.translateError(error.message),
        };
      }

      return {
        success: true,
        message: 'Redirecionando para o Google...',
      };
    } catch (error) {
      console.error('Erro no Google OAuth:', error);
      return {
        success: false,
        error: 'Erro interno do servidor',
      };
    }
  }

  // Login com GitHub
  async signInWithGitHub(): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: this.getRedirectUrl(),
        },
      });

      if (error) {
        return {
          success: false,
          error: this.translateError(error.message),
        };
      }

      return {
        success: true,
        message: 'Redirecionando para o GitHub...',
      };
    } catch (error) {
      console.error('Erro no GitHub OAuth:', error);
      return {
        success: false,
        error: 'Erro interno do servidor',
      };
    }
  }

  // Obter usuário atual
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        console.log('Nenhum usuário autenticado ou erro:', error?.message);
        return null;
      }

      return {
        id: user.id,
        email: user.email!,
        fullName: user.user_metadata?.full_name || user.user_metadata?.display_name,
        emailConfirmed: user.email_confirmed_at ? true : false,
        createdAt: user.created_at,
      };
    } catch (error) {
      console.error('Erro ao obter usuário atual:', error);
      return null;
    }
  }

  // Reenviar email de confirmação
  async resendConfirmation(email: string): Promise<AuthResponse> {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email,
        options: {
          emailRedirectTo: this.getRedirectUrl(),
        },
      });

      if (error) {
        return {
          success: false,
          error: this.translateError(error.message),
        };
      }

      return {
        success: true,
        message: 'Email de confirmação reenviado!',
      };
    } catch (error) {
      console.error('Erro ao reenviar confirmação:', error);
      return {
        success: false,
        error: 'Erro interno do servidor',
      };
    }
  }

  // Redefinir senha
  async resetPassword(email: string): Promise<AuthResponse> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        return {
          success: false,
          error: this.translateError(error.message),
        };
      }

      return {
        success: true,
        message: 'Email de redefinição de senha enviado!',
      };
    } catch (error) {
      console.error('Erro ao redefinir senha:', error);
      return {
        success: false,
        error: 'Erro interno do servidor',
      };
    }
  }

  // Listener para mudanças de autenticação
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);
      
      if (session?.user) {
        const user: AuthUser = {
          id: session.user.id,
          email: session.user.email!,
          fullName: session.user.user_metadata?.full_name || session.user.user_metadata?.display_name,
          emailConfirmed: session.user.email_confirmed_at ? true : false,
          createdAt: session.user.created_at,
        };
        callback(user);
      } else {
        callback(null);
      }
    });
  }

  // Traduzir erros do Supabase para português
  private translateError(error: string): string {
    const translations: Record<string, string> = {
      'Invalid login credentials': 'Email ou senha incorretos',
      'Email not confirmed': 'Email não confirmado. Verifique sua caixa de entrada.',
      'User already registered': 'Este email já está registrado',
      'Password should be at least 6 characters': 'A senha deve ter pelo menos 6 caracteres',
      'Invalid email': 'Email inválido',
      'Signup is disabled': 'Cadastro está desabilitado',
      'Email rate limit exceeded': 'Muitas tentativas. Tente novamente mais tarde.',
      'Invalid refresh token': 'Sessão expirada. Faça login novamente.',
      'User not found': 'Usuário não encontrado',
      'Session not found': 'Sessão não encontrada',
      'Token has expired or is invalid': 'Token expirado ou inválido',
      'To signup, please provide your email': 'Para cadastrar-se, forneça seu email',
      'Unable to validate email address: invalid format': 'Formato de email inválido',
      'Password should be at least 6 characters.': 'A senha deve ter pelo menos 6 caracteres',
    };

    return translations[error] || error;
  }
}

export const authService = new AuthService(); 