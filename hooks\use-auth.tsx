'use client'

import React, { createContext, useContext, useEffect, useState } from 'react';
import { authService } from '@/lib/auth';
import type { AuthUser } from '@/types/auth';

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Função para atualizar o usuário
  const refreshUser = async () => {
    try {
      console.log('Atualizando informações do usuário...');
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
      console.log('Usuário atualizado:', currentUser?.email || 'nenhum usuário');
    } catch (error) {
      console.error('Erro ao buscar usuário:', error);
      setUser(null);
    }
  };

  // Função de logout
  const signOut = async () => {
    try {
      console.log('Realizando logout...');
      const result = await authService.signOut();
      if (result.success) {
        setUser(null);
        console.log('Logout realizado com sucesso');
      } else {
        console.error('Erro no logout:', result.error);
      }
    } catch (error) {
      console.error('Erro no logout:', error);
    }
  };

  useEffect(() => {
    console.log('Inicializando AuthProvider...');
    
    // Verificar usuário atual na inicialização
    const initializeAuth = async () => {
      try {
        await refreshUser();
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listener para mudanças de autenticação
    const { data: { subscription } } = authService.onAuthStateChange((authUser) => {
      console.log('Estado de autenticação alterado:', authUser?.email || 'logout');
      setUser(authUser);
      setLoading(false);
    });

    return () => {
      console.log('Limpando listener de autenticação');
      subscription?.unsubscribe();
    };
  }, []);

  const value = {
    user,
    loading,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 