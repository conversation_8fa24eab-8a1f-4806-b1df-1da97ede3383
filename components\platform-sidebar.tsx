"use client";

import * as React from "react";
import { Plus, Shield, Globe, Hash, MessageCircle, User, Video, Phone, TrendingUp, Monitor } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

// Ícones SVG customizados para redes sociais com cores originais das marcas
const FacebookIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="#1877F2">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
);

const InstagramIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24">
    <defs>
      <linearGradient id="instagram-gradient-platform" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E4405F"/>
        <stop offset="50%" stopColor="#F77737"/>
        <stop offset="100%" stopColor="#FCAF45"/>
      </linearGradient>
    </defs>
    <path fill="url(#instagram-gradient-platform)" d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
);

const TwitterIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="#000000">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const LinkedinIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="#0A66C2">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
);

const PinterestIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="#E60023">
    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624.009 11.99-5.358 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
  </svg>
);

const TikTokIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24">
    <defs>
      <linearGradient id="tiktok-gradient-platform" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF0050"/>
        <stop offset="50%" stopColor="#00F2EA"/>
        <stop offset="100%" stopColor="#000000"/>
      </linearGradient>
    </defs>
    <path fill="url(#tiktok-gradient-platform)" d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
  </svg>
);

const BlueSkyIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="#0085ff">
    <path d="M12 10.8c-1.087-2.114-4.046-6.053-6.798-7.995C2.566.944 1.561 1.266.902 1.565.139 1.908 0 3.08 0 3.768c0 .69.378 5.65.624 6.479.815 2.736 3.713 3.66 6.383 3.364.136-.02.275-.039.415-.056-.138.022-.276.04-.415.056-3.912.58-7.387 2.005-2.83 7.078 5.013 5.19 6.87-1.113 7.823-4.308.953 3.195 2.05 9.271 7.733 4.308 4.267-4.308 1.172-6.498-2.74-7.078a8.741 8.741 0 0 1-.415-.056c.14.017.279.036.415.056 2.67.297 5.568-.628 6.383-3.364.246-.828.624-5.79.624-6.478 0-.69-.139-1.861-.902-2.206-.659-.298-1.664-.62-4.3 1.24C16.046 4.748 13.087 8.687 12 10.8Z"/>
  </svg>
);

const ThreadsIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24">
    <defs>
      <linearGradient id="threads-gradient-platform" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E4405F"/>
        <stop offset="50%" stopColor="#F77737"/>
        <stop offset="100%" stopColor="#FCAF45"/>
      </linearGradient>
    </defs>
    <path fill="url(#threads-gradient-platform)" d="M12.186 24h-.007c-3.581-.024-6.334-1.205-8.184-3.509C2.35 18.44 1.5 15.586 1.472 12.01v-.017c.03-3.579.879-6.43 2.525-8.482C5.845 1.205 8.6.024 12.18 0h.014c2.746.02 5.043.725 6.826 2.098 1.677 1.29 2.858 3.13 3.509 5.467l-2.04.569c-1.104-3.96-3.898-5.984-8.304-6.015-2.91.022-5.11.936-6.54 2.717C4.307 6.504 3.616 8.914 3.589 12c.027 3.086.718 5.496 2.057 7.164 1.43 1.781 3.63 2.695 6.54 2.717 2.623-.02 4.358-.631 5.8-2.045 1.647-1.613 1.618-3.593 1.09-4.798-.31-.71-.873-1.3-1.634-1.75-.192 1.352-.622 2.446-1.284 3.272-.886 1.102-2.14 1.704-3.73 1.79-1.202.065-2.361-.218-3.259-.801-1.063-.689-1.685-1.74-1.752-2.964-.065-1.19.408-2.285 1.33-3.082.88-.76 2.119-1.207 3.583-1.291a13.853 13.853 0 0 1 3.02.142c-.126-.742-.375-1.332-.743-1.757-.438-.507-1.06-.756-1.857-.744-.92.015-1.668.29-2.22.818-.348.332-.647.869-.875 1.615l-2.06-.569c.397-1.49 1.066-2.666 2.058-3.622 1.262-1.216 2.943-1.827 5.096-1.85 1.688-.017 3.17.66 4.413 2.013 1.19 1.295 1.789 3.146 1.789 5.516v.027a9.712 9.712 0 0 1-.064 1.046l2.039.568c.02-.37.031-.751.031-1.143v-.027c0-3.07-.818-5.637-2.431-7.624C18.768 1.718 15.636.768 12.186 0z"/>
  </svg>
);

const TwitchIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="#9146FF">
    <path d="M11.571 4.714h1.715v5.143H11.57zm4.715 0H18v5.143h-1.714zM6 0L1.714 4.286v15.428h5.143V24l4.286-4.286h3.428L22.286 12V0zm14.571 11.143l-3.428 3.428h-3.429l-3 3v-3H6.857V1.714h13.714Z"/>
  </svg>
);

const YoutubeIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="#FF0000">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
);

// Dados das plataformas exatamente como mostrado na imagem
const platformData = [
  {
    id: "summary",
    name: "Summary",
    icon: "summary",
    isActive: true,
    hasShield: false,
  },
  {
    id: "web",
    name: "Web",
    icon: Globe,
    isActive: false,
    hasShield: false,
  },
  {
    id: "facebook",
    name: "Facebook",
    icon: FacebookIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "instagram",
    name: "Instagram",
    icon: InstagramIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "threads",
    name: "Threads",
    icon: ThreadsIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "twitter",
    name: "Twitter",
    icon: TwitterIcon,
    isActive: false,
    hasShield: true,
  },
  {
    id: "bluesky",
    name: "Bluesky",
    icon: BlueSkyIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "linkedin",
    name: "LinkedIn",
    icon: LinkedinIcon,
    isActive: false,
    hasShield: true,
  },
  {
    id: "pinterest",
    name: "Pinterest",
    icon: PinterestIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "tiktok",
    name: "TikTok",
    icon: TikTokIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "google-business",
    name: "Google Business Profile",
    icon: Monitor,
    isActive: false,
    hasShield: false,
  },
  {
    id: "youtube",
    name: "YouTube",
    icon: YoutubeIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "twitch",
    name: "Twitch",
    icon: TwitchIcon,
    isActive: false,
    hasShield: false,
  },
  {
    id: "meta-ads",
    name: "Meta Ads",
    icon: TrendingUp,
    isActive: false,
    hasShield: false,
  },
  {
    id: "google-ads",
    name: "Google Ads",
    icon: TrendingUp,
    isActive: false,
    hasShield: false,
  },
  {
    id: "tiktok-ads",
    name: "TikTok Ads",
    icon: Video,
    isActive: false,
    hasShield: false,
  },
];

interface PlatformItemProps {
  platform: typeof platformData[0];
  onClick?: () => void;
}

function PlatformItem({ platform, onClick }: PlatformItemProps) {
  const IconComponent = platform.icon === "summary" ? null : platform.icon as React.ComponentType<{ className?: string }>;

  return (
    <div
      className={cn(
        "flex items-center justify-between w-full px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800",
        platform.isActive && "bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900"
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-3 flex-1">
        {/* Ícone da plataforma */}
        <div className="flex items-center justify-center w-5 h-5">
          {platform.icon === "summary" ? (
            <div className="w-4 h-4 bg-white dark:bg-gray-900 rounded-sm flex items-center justify-center">
              <div className="w-2 h-2 bg-gray-900 dark:bg-white rounded-sm"></div>
            </div>
          ) : IconComponent ? (
            <IconComponent className="w-5 h-5" />
          ) : null}
        </div>

        {/* Nome da plataforma */}
        <span 
          className={cn(
            "text-sm font-medium truncate",
            platform.isActive 
              ? "text-white " 
              : "text-gray-900 "
          )}
        >
          {platform.name}
        </span>
      </div>

      {/* Ícones à direita */}
      <div className="flex items-center gap-2">
        {/* Escudo amarelo para algumas plataformas */}
        {platform.hasShield && (
          <div className="w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
            <Shield className="w-3 h-3 text-yellow-900" />
          </div>
        )}

        {/* Botão + */}
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "w-6 h-6 p-0 rounded-full transition-colors",
            platform.isActive
              ? "hover:bg-gray-700 dark:hover:bg-gray-300 text-white dark:text-gray-900"
              : "hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
          )}
        >
          <Plus className="w-3 h-3" />
        </Button>
      </div>
    </div>
  );
}

export function PlatformSidebar() {
  const [activePlatform, setActivePlatform] = React.useState("summary");

  const handlePlatformClick = async (platformId: string) => {
    setActivePlatform(platformId);
    
    // Se for Instagram, inicia o processo de autenticação
    if (platformId === "instagram") {
      try {
        console.log("🚀 Iniciando autenticação do Instagram...");
        
        // Chama a API para obter a URL de autenticação
        const response = await fetch('/auth/instagram/login');
        const data = await response.json();
        
        if (data.success && data.authUrl) {
          console.log("✅ URL de autenticação obtida:", data.authUrl);
          
          // Redireciona o usuário para a página de autorização do Instagram
          window.location.href = data.authUrl;
        } else {
          console.error("❌ Erro ao obter URL de autenticação:", data);
          alert("Erro ao conectar com Instagram. Verifique as configurações.");
        }
      } catch (error) {
        console.error("❌ Erro na requisição de autenticação:", error);
        alert("Erro de rede ao tentar conectar com Instagram.");
      }
    }
    
    // Se for TikTok, inicia o processo de autenticação via popup
    if (platformId === "tiktok") {
      try {
        console.log("🚀 Iniciando autenticação TikTok via popup...");
        
        // Chama a API para obter a URL de autenticação (com parâmetro popup)
        const response = await fetch('/api/tiktok/auth/login?popup=true');
        const data = await response.json();
        
        if (!response.ok || !data.authUrl) {
          throw new Error(data.error || 'Erro ao obter URL de autenticação');
        }

        console.log("✅ URL de autenticação TikTok obtida:", data.authUrl);
        console.log("🔒 Proteções ativas:", data.security);
        console.log("📋 Permissões solicitadas:", data.scopes);

        // Configurações do popup
        const popupWidth = 500;
        const popupHeight = 700;
        const left = (window.screen.width - popupWidth) / 2;
        const top = (window.screen.height - popupHeight) / 2;
        
        const popupOptions = [
          `width=${popupWidth}`,
          `height=${popupHeight}`,
          `left=${left}`,
          `top=${top}`,
          'scrollbars=yes',
          'resizable=yes',
          'status=no',
          'location=no',
          'toolbar=no',
          'menubar=no'
        ].join(',');

        // Abre o popup
        console.log('🪟 Abrindo popup de autenticação...');
        const popup = window.open(data.authUrl, 'tiktok_auth', popupOptions);
        
        if (!popup) {
          // Fallback: redireciona se popup foi bloqueado
          console.warn('🚫 Popup bloqueado pelo navegador. Usando redirecionamento tradicional...');
          alert('⚠️ Popup bloqueado!\n\nVamos abrir a autenticação TikTok na mesma janela.\nVocê será redirecionado de volta após a autorização.');
          window.location.href = '/api/tiktok/auth/login';
          return;
        }

        // Escuta mensagens do popup
        const handleMessage = (event: MessageEvent) => {
          // Verificação de segurança da origem
          if (event.origin !== window.location.origin) {
            return;
          }

          const { type, success, error: authError } = event.data;

          if (type === 'TIKTOK_AUTH_RESULT') {
            // Remove o listener
            window.removeEventListener('message', handleMessage);
            
            // Fecha o popup
            if (popup && !popup.closed) {
              popup.close();
            }

            if (success) {
              console.log('✅ Autenticação TikTok realizada com sucesso via popup!');
              // Recarrega a página para atualizar o estado
              window.location.reload();
            } else {
              console.error('❌ Erro na autenticação TikTok:', authError);
              alert('❌ Erro na autenticação TikTok. Tente novamente.');
            }
          }
        };

        // Adiciona listener para mensagens
        window.addEventListener('message', handleMessage);

        // Monitora se o popup foi fechado manualmente
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            window.removeEventListener('message', handleMessage);
            console.log('🔄 Popup de autenticação TikTok foi fechado');
          }
        }, 1000);

        // Cleanup após 5 minutos (timeout)
        setTimeout(() => {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          if (popup && !popup.closed) {
            popup.close();
          }
        }, 5 * 60 * 1000);

      } catch (error) {
        console.error("❌ Erro crítico na autenticação TikTok:", error);
        
        // Mostra mensagem de erro e usa fallback
        console.warn('🔄 Usando método de redirecionamento como fallback...');
        alert('⚠️ Erro no popup!\n\nVamos usar o método tradicional de autenticação.\nVocê será redirecionado para o TikTok.');
        
        // Fallback: redireciona sem popup
        window.location.href = '/api/tiktok/auth/login';
      }
    }
  };

  return (
    <div className="w-[280px] h-full bg-white flex flex-col">
      {/* Conteúdo da sidebar */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-1">
          {platformData.map((platform) => (
            <PlatformItem
              key={platform.id}
              platform={{
                ...platform,
                isActive: platform.id === activePlatform,
              }}
              onClick={() => handlePlatformClick(platform.id)}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
} 