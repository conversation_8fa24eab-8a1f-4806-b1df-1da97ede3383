"use client";

import { useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';

/**
 * Página de Callback de Autenticação
 * 
 * Detecta se está executando em um popup e:
 * - Se popup: envia mensagem para janela pai e fecha
 * - Se não popup: redireciona normalmente para dashboard
 */
function CallbackContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const handleCallback = () => {
      // Obtém os parâmetros da URL
      const tikTokAuth = searchParams.get('tiktok_auth');
      const tikTokUser = searchParams.get('tiktok_user');
      const tikTokData = searchParams.get('tiktok_data');
      const errorMessage = searchParams.get('error_message');

      // Detecta se está em um popup
      const isPopup = window.opener && window.opener !== window;

      if (isPopup) {
        // Está em popup - envia mensagem imediatamente e fecha
        console.log('🔄 Popup detectado - processando resultado...');
        
        const result = {
          type: 'TIKTOK_AUTH_RESULT',
          success: tikTokAuth === 'success',
          error: errorMessage ? decodeURIComponent(errorMessage) : null,
          user: tikTokUser || null,
          data: tikTokData ? JSON.parse(decodeURIComponent(tikTokData)) : null
        };

        // Envia mensagem para a janela pai
        window.opener.postMessage(result, window.location.origin);
        
        // Fecha o popup imediatamente
        window.close();
      } else {
        // Não está em popup - redireciona normalmente
        console.log('🔄 Redirecionamento normal para dashboard');
        
        const dashboardUrl = new URL('/dashboard', window.location.origin);
        if (tikTokAuth) dashboardUrl.searchParams.set('tiktok_auth', tikTokAuth);
        if (tikTokUser) dashboardUrl.searchParams.set('tiktok_user', tikTokUser);
        if (tikTokData) dashboardUrl.searchParams.set('tiktok_data', tikTokData);
        if (errorMessage) dashboardUrl.searchParams.set('error_message', errorMessage);
        
        router.replace(dashboardUrl.toString());
      }
    };

    // Executa imediatamente sem delay
    handleCallback();
  }, [searchParams, router]);

  // Só mostra a tela de loading se NÃO for popup
  const isPopup = typeof window !== 'undefined' && window.opener && window.opener !== window;
  
  if (isPopup) {
    // Para popup, não mostra nada (vai fechar imediatamente)
    return null;
  }

  // Para navegação normal, mostra tela de loading
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pink-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      <div className="text-center space-y-4">
        <div className="w-16 h-16 mx-auto border-4 border-pink-500 border-t-transparent rounded-full animate-spin"></div>
        <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300">
          Finalizando autenticação...
        </h2>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Redirecionando para o dashboard
        </p>
      </div>
    </div>
  );
}

/**
 * Página principal do callback com Suspense
 */
export default function CallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto border-4 border-pink-500 border-t-transparent rounded-full animate-spin"></div>
          <p>Carregando...</p>
        </div>
      </div>
    }>
      <CallbackContent />
    </Suspense>
  );
} 