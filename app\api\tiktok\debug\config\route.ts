import { NextRequest, NextResponse } from 'next/server';
import { tiktokAPI } from '@/lib/tiktok-api';

/**
 * Endpoint de debug para verificar configurações do TikTok
 * GET /api/tiktok/debug/config
 * 
 * Funcionalidades:
 * - Validação de configurações
 * - Teste de conectividade
 * - Geração de URL de teste
 * - Verificação de scopes
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Debug: Verificando configurações TikTok');
    
    // Gera uma URL de teste com scope básico (sempre disponível)
    const testState = 'debug_test_state_' + Date.now();
    const scopes = [
      'user.info.basic',
      'user.info.profile',
      'user.info.stats'
    ];
    
    const authUrl = tiktokAPI.getAuthorizationUrl(scopes, testState);
    
    // Verifica configurações de ambiente
    const config = {
      client_key: process.env.TIKTOK_CLIENT_KEY ? 
        process.env.TIKTOK_CLIENT_KEY.substring(0, 10) + '...' : 
        'NÃO CONFIGURADO',
      redirect_uri: process.env.TIKTOK_REDIRECT_URI || 'NÃO CONFIGURADO',
      client_secret_configured: !!process.env.TIKTOK_CLIENT_SECRET,
      base_url: process.env.NEXT_PUBLIC_BASE_URL || 'NÃO CONFIGURADO',
      environment: process.env.NODE_ENV || 'development',
      debug_mode: process.env.DEBUG_MODE === 'true'
    };

    console.log('✅ Configurações TikTok verificadas:', config);
    
    return NextResponse.json({
      success: true,
      message: 'Configurações do TikTok carregadas e validadas',
      config: config,
      testAuthUrl: authUrl,
      validScopes: scopes,
      apiEndpoints: {
        authorization: 'https://www.tiktok.com/v2/auth/authorize/',
        token: 'https://open.tiktokapis.com/v2/oauth/token/',
        user_info: 'https://open.tiktokapis.com/v2/user/info/'
      },
      instructions: {
        step1: 'Verifique se o Client Key está configurado',
        step2: 'Verifique se o Client Secret está configurado', 
        step3: 'Verifique se o Redirect URI está correto',
        step4: 'Configure no TikTok for Developers: ' + (process.env.TIKTOK_REDIRECT_URI || 'URL não configurada'),
                 step5: 'Teste a autenticação usando o testAuthUrl fornecido',
                   step6: 'Usando scopes completos (user.info.basic + user.info.profile + user.info.stats) que não requerem aprovação especial'
      },
      security: {
        csrf_protection: true,
        state_validation: true,
        secure_token_exchange: true,
        oauth2_compliant: true
      },
                     troubleshooting: {
          scope_info: 'Usando user.info.basic + user.info.profile + user.info.stats (sempre disponíveis, não requerem aprovação)',
                   scope_expansion: 'Para mais dados, habilite scopes adicionais no app TikTok: video.list, portability.postsandprofile.single',
         redirect_uri_error: 'Deve corresponder exatamente ao configurado no app TikTok',
         client_credentials_error: 'Verifique Client Key e Client Secret no TikTok for Developers'
       },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro no debug TikTok:', error);
    
    return NextResponse.json({
      success: false,
      error: 'debug_failed',
      message: 'Erro ao verificar configurações TikTok',
      details: error.message,
             troubleshooting: {
         check_env_vars: 'Verifique TIKTOK_CLIENT_KEY, TIKTOK_CLIENT_SECRET e TIKTOK_REDIRECT_URI',
         check_app_config: 'Verifique configurações no TikTok for Developers',
                   scope_info: 'Usando user.info.basic + user.info.profile + user.info.stats (não requerem aprovação especial)',
         next_steps: 'Se funcionar, você pode habilitar scopes adicionais posteriormente'
       },
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
} 