import { NextRequest, NextResponse } from 'next/server';
import { instagramAPI } from '@/lib/instagram-api';

/**
 * Endpoint para extrair insights do Instagram
 * GET /api/instagram/insights
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type') || 'user'; // user, media, story, reel, all
    const mediaId = searchParams.get('media_id');
    const period = searchParams.get('period') || 'day';
    const limit = parseInt(searchParams.get('limit') || '10');
    const accessToken = searchParams.get('access_token');

    if (!accessToken) {
      return NextResponse.json({
        success: false,
        error: 'missing_access_token',
        message: 'Token de acesso é obrigatório'
      }, { status: 400 });
    }

    let insights;

    switch (type) {
      case 'user': {
        // Insights do perfil do usuário
        const userProfile = await instagramAPI.getUserProfile(accessToken);
        insights = await instagramAPI.getUserInsights(
          userProfile.id, 
          accessToken, 
          undefined, 
          period as 'day' | 'week' | 'days_28'
        );
        break;
      }

      case 'media': {
        // Insights de mídia específica
        if (!mediaId) {
          return NextResponse.json({
            success: false,
            error: 'missing_media_id',
            message: 'ID da mídia é obrigatório para insights de mídia'
          }, { status: 400 });
        }
        insights = await instagramAPI.getCompleteMediaInsights(mediaId, accessToken);
        break;
      }

      case 'story': {
        // Insights de story específica
        if (!mediaId) {
          return NextResponse.json({
            success: false,
            error: 'missing_media_id',
            message: 'ID da story é obrigatório para insights de story'
          }, { status: 400 });
        }
        insights = await instagramAPI.getStoryInsights(mediaId, accessToken);
        break;
      }

      case 'reel': {
        // Insights de reel específico
        if (!mediaId) {
          return NextResponse.json({
            success: false,
            error: 'missing_media_id',
            message: 'ID do reel é obrigatório para insights de reel'
          }, { status: 400 });
        }
        insights = await instagramAPI.getReelsInsights(mediaId, accessToken);
        break;
      }

      case 'all': {
        // Insights de todas as mídias recentes
        const userProfile2 = await instagramAPI.getUserProfile(accessToken);
        insights = await instagramAPI.getAllRecentMediaInsights(
          userProfile2.id, 
          accessToken, 
          limit
        );
        break;
      }

      case 'historical': {
        // Insights históricos do usuário
        const userProfile3 = await instagramAPI.getUserProfile(accessToken);
        insights = await instagramAPI.getHistoricalUserInsights(
          userProfile3.id, 
          accessToken, 
          period as 'day' | 'week' | 'days_28'
        );
        break;
      }

      default:
        return NextResponse.json({
          success: false,
          error: 'invalid_type',
          message: 'Tipo de insight inválido. Use: user, media, story, reel, all, historical'
        }, { status: 400 });
    }

    console.log(`✅ Insights obtidos com sucesso (tipo: ${type})`);

    return NextResponse.json({
      success: true,
      type: type,
      insights: insights,
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('❌ Erro ao obter insights:', error);

    // Trata erros específicos da API do Instagram
    if (error && typeof error === 'object' && 'response' in error && 
        error.response && typeof error.response === 'object' && 'data' in error.response &&
        error.response.data && typeof error.response.data === 'object' && 'error' in error.response.data) {
      
      const apiError = error.response.data.error as { code?: string; message?: string };
      
      return NextResponse.json({
        success: false,
        error: apiError.code || 'instagram_api_error',
        message: apiError.message || 'Erro na API do Instagram',
        details: apiError
      }, { status: 400 });
    }

    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Erro interno do servidor',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * Endpoint POST para insights com parâmetros no body
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, mediaId, period, limit, accessToken, metrics, breakdown } = body;

    if (!accessToken) {
      return NextResponse.json({
        success: false,
        error: 'missing_access_token',
        message: 'Token de acesso é obrigatório'
      }, { status: 400 });
    }

    let insights;

    switch (type) {
      case 'detailed': {
        // Insights detalhados com breakdown
        if (!mediaId || !metrics || !breakdown) {
          return NextResponse.json({
            success: false,
            error: 'missing_parameters',
            message: 'Para insights detalhados, media_id, metrics e breakdown são obrigatórios'
          }, { status: 400 });
        }
        insights = await instagramAPI.getDetailedInsights(mediaId, accessToken, metrics, breakdown);
        break;
      }

      case 'custom_media': {
        // Insights de mídia com métricas customizadas
        if (!mediaId) {
          return NextResponse.json({
            success: false,
            error: 'missing_media_id',
            message: 'ID da mídia é obrigatório'
          }, { status: 400 });
        }
        insights = await instagramAPI.getMediaInsights(mediaId, accessToken, metrics, breakdown);
        break;
      }

      case 'custom_user': {
        // Insights de usuário com métricas customizadas
        const userProfile = await instagramAPI.getUserProfile(accessToken);
        insights = await instagramAPI.getUserInsights(
          userProfile.id, 
          accessToken, 
          metrics, 
          period
        );
        break;
      }

      default:
        return NextResponse.json({
          success: false,
          error: 'invalid_type',
          message: 'Tipo de insight inválido para POST. Use: detailed, custom_media, custom_user'
        }, { status: 400 });
    }

    console.log(`✅ Insights customizados obtidos com sucesso (tipo: ${type})`);

    return NextResponse.json({
      success: true,
      type: type,
      insights: insights,
      parameters: { metrics, breakdown, period },
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('❌ Erro ao obter insights customizados:', error);

    if (error && typeof error === 'object' && 'response' in error && 
        error.response && typeof error.response === 'object' && 'data' in error.response &&
        error.response.data && typeof error.response.data === 'object' && 'error' in error.response.data) {
      
      const apiError = error.response.data.error as { code?: string; message?: string };
      
      return NextResponse.json({
        success: false,
        error: apiError.code || 'instagram_api_error',
        message: apiError.message || 'Erro na API do Instagram',
        details: apiError
      }, { status: 400 });
    }

    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Erro interno do servidor',
      details: errorMessage
    }, { status: 500 });
  }
} 