# Guia de Correção de Problemas de Hidratação

## Problema Identificado

O erro de hidratação estava ocorrendo devido a incompatibilidades entre os atributos da tag `<html>` renderizada no servidor versus no cliente, especificamente relacionada ao sistema de temas do `next-themes`.

## Soluções Implementadas

### 1. Layout Root (`app/layout.tsx`)
- Adicionado `suppressHydrationWarning` na tag HTML
- Isso evita warnings quando atributos como `className="dark"` e `style` são diferentes entre servidor e cliente

### 2. Configuração do Tema (`app/providers.tsx`)
- Alterado `defaultTheme` de `"system"` para `"dark"`
- Desabilitado `enableSystem` para evitar detecção automática do tema do sistema
- Adicionado `storageKey` personalizado para persistência
- Mantido `disableTransitionOnChange` para performance

### 3. Componente ThemeWrapper (`components/ui/theme-wrapper.tsx`)
- Criado wrapper seguro para evitar problemas de hidratação
- Hook `useSafeTheme` que só aplica tema após a hidratação completa
- Fallback para tema "dark" durante o processo de hidratação

### 4. Componente Sonner Atualizado
- Substituído `useTheme` por `useSafeTheme`
- Tema padrão alterado de "system" para "dark"

## Estratégias de Prevenção

### Para Componentes Client-Side
```typescript
'use client'

import { useEffect, useState } from 'react'

export function ComponenteSeguro() {
  const [mounted, setMounted] = useState(false)
  
  useEffect(() => {
    setMounted(true)
  }, [])
  
  if (!mounted) {
    return <div>Carregando...</div>
  }
  
  return <div>Conteúdo após hidratação</div>
}
```

### Para Uso de Temas
```typescript
import { useSafeTheme } from '@/components/ui/theme-wrapper'

export function ComponenteComTema() {
  const { theme, mounted } = useSafeTheme()
  
  if (!mounted) {
    return <div className="dark">Fallback para tema escuro</div>
  }
  
  return <div className={theme}>Conteúdo com tema correto</div>
}
```

## Checklist de Prevenção

- [ ] Usar `suppressHydrationWarning` apenas quando necessário
- [ ] Evitar `Date.now()`, `Math.random()` em renderização inicial
- [ ] Não usar `typeof window !== 'undefined'` em componentes renderizados
- [ ] Garantir consistência entre servidor e cliente para formatação de datas
- [ ] Validar HTML aninhado corretamente
- [ ] Testar com extensões do navegador desabilitadas

## Debugging

Para identificar problemas de hidratação:

1. Verificar console do navegador para warnings específicos
2. Comparar HTML renderizado no servidor vs cliente
3. Usar React Developer Tools para inspecionar props
4. Testar em modo incógnito (sem extensões)

## Configurações Recomendadas

### next-themes
```typescript
<ThemeProvider
  attribute="class"
  defaultTheme="dark" // Tema fixo para evitar inconsistências
  enableSystem={false} // Desabilitar detecção automática
  disableTransitionOnChange
  storageKey="app-theme"
>
```

### Verificação de Extensões do Navegador
O erro original mostrava `crxemulator=""` que indica interferência de extensão.
Para testar sem interferências:
- Usar modo incógnito
- Desabilitar temporariamente extensões
- Testar em diferentes navegadores 