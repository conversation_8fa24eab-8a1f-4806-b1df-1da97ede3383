import React from 'react';

interface PasswordRequirementsProps {
  password: string;
}

interface Requirement {
  text: string;
  isValid: (password: string) => boolean;
}

export const PasswordRequirements: React.FC<PasswordRequirementsProps> = ({ password }) => {
  const requirements: Requirement[] = [
    {
      text: '<PERSON>elo menos 12 caracteres',
      isValid: (pwd) => pwd.length >= 12,
    },
    {
      text: 'uma letra maiúscula',
      isValid: (pwd) => /[A-Z]/.test(pwd),
    },
    {
      text: 'uma letra minúscula',
      isValid: (pwd) => /[a-z]/.test(pwd),
    },
    {
      text: 'um caractere especial',
      isValid: (pwd) => /[!@#$%^&*(),.?":{}|<>]/.test(pwd),
    },
  ];

  const RequirementIcon: React.FC<{ isValid: boolean }> = ({ isValid }) => (
    <div className="box-border flex w-5 h-5 justify-center items-center">
      <svg width="20" height="24" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        {isValid ? (
          <path d="M9.0188 13.9399L15.1855 7.77327L16.3522 8.93994L9.0188 16.2733L4.68555 11.9399L5.85221 10.7733L9.0188 13.9399Z" fill="#22C55E"/>
        ) : (
          <path d="M10.6856 14.7733C10.9217 14.7733 11.1197 14.6934 11.2794 14.5337C11.4391 14.374 11.519 14.1761 11.519 13.9399C11.519 13.7038 11.4391 13.5059 11.2794 13.3462C11.1197 13.1865 10.9217 13.1066 10.6856 13.1066C10.4495 13.1066 10.2516 13.1865 10.0919 13.3462C9.93216 13.5059 9.8523 13.7038 9.8523 13.9399C9.8523 14.1761 9.93216 14.374 10.0919 14.5337C10.2516 14.6934 10.4495 14.7733 10.6856 14.7733ZM9.8523 11.4399H11.519V6.43994H9.8523V11.4399ZM10.6856 18.9399C9.53285 18.9399 8.44952 18.7212 7.43563 18.2837C6.42174 17.8462 5.5398 17.2524 4.7898 16.5024C4.0398 15.7524 3.44605 14.8705 3.00855 13.8566C2.57104 12.8427 2.35229 11.7594 2.35229 10.6066C2.35229 9.45383 2.57104 8.3705 3.00855 7.35661C3.44605 6.34272 4.0398 5.46077 4.7898 4.71077C5.5398 3.96077 6.42174 3.36702 7.43563 2.92952C8.44952 2.49202 9.53285 2.27327 10.6856 2.27327C11.8384 2.27327 12.9217 2.49202 13.9356 2.92952C14.9495 3.36702 15.8315 3.96077 16.5815 4.71077C17.3315 5.46077 17.9252 6.34272 18.3627 7.35661C18.8002 8.3705 19.019 9.45383 19.019 10.6066C19.019 11.7594 18.8002 12.8427 18.3627 13.8566C17.9252 14.8705 17.3315 15.7524 16.5815 16.5024C15.8315 17.2524 14.9495 17.8462 13.9356 18.2837C12.9217 18.7212 11.8384 18.9399 10.6856 18.9399ZM10.6856 17.2733C12.5467 17.2733 14.1231 16.6274 15.4148 15.3358C16.7065 14.0441 17.3523 12.4677 17.3523 10.6066C17.3523 8.7455 16.7065 7.16911 15.4148 5.87744C14.1231 4.58577 12.5467 3.93994 10.6856 3.93994C8.82452 3.93994 7.24813 4.58577 5.95646 5.87744C4.6648 7.16911 4.01896 8.7455 4.01896 10.6066C4.01896 12.4677 4.6648 14.0441 5.95646 15.3358C7.24813 16.6274 8.82452 17.2733 10.6856 17.2733Z" fill="#C00803"/>
        )}
      </svg>
    </div>
  );

  return (
    <div className="mt-2.5">
      <div className="box-border text-[#1C1C1C] text-base font-normal leading-6 tracking-[-0.16px] mb-0.5">
        As senhas devem conter:
      </div>
      {requirements.map((requirement, index) => (
        <div key={index} className="box-border flex items-center gap-2">
          <RequirementIcon isValid={requirement.isValid(password)} />
          <div className={`box-border text-base font-normal leading-6 tracking-[-0.16px] ${
            requirement.isValid(password) ? 'text-green-600' : 'text-[#1C1C1C]'
          }`}>
            {requirement.text}
          </div>
        </div>
      ))}
    </div>
  );
};
