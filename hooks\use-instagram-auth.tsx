"use client";

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

interface InstagramUser {
  id: string;
  username: string;
  name: string;
  profile_picture_url?: string;
  followers_count?: number;
  media_count?: number;
  account_type: string;
}

interface InstagramAuthState {
  isAuthenticated: boolean;
  user: InstagramUser | null;
  isLoading: boolean;
  error: string | null;
  authStatus: 'idle' | 'pending' | 'success' | 'error';
}

export function useInstagramAuth() {
  const [authState, setAuthState] = useState<InstagramAuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true, // Inicia como carregando para verificar localStorage
    error: null,
    authStatus: 'idle'
  });

  const searchParams = useSearchParams();

  useEffect(() => {
    console.log('🔍 useInstagramAuth useEffect executado');
    
    // Verifica se há parâmetros de retorno da autenticação
    const authResult = searchParams.get('instagram_auth');
    const username = searchParams.get('instagram_user');
    const errorMessage = searchParams.get('error_message');
    const instagramData = searchParams.get('instagram_data');
    
    console.log('📊 Parâmetros da URL:', { authResult, username, hasInstagramData: !!instagramData });

    if (authResult === 'success' && username) {
      let userData: InstagramUser = {
        id: 'temp_id',
        username: username,
        name: username,
        account_type: 'BUSINESS'
      };

      // Se temos dados completos do Instagram, usa eles
      if (instagramData) {
        try {
          const parsedData = JSON.parse(decodeURIComponent(instagramData));
          userData = {
            id: parsedData.user.id,
            username: parsedData.user.username,
            name: parsedData.user.name,
            profile_picture_url: parsedData.user.profile_picture_url,
            followers_count: parsedData.user.followers_count,
            media_count: parsedData.user.media_count,
            account_type: parsedData.user.account_type
          };

          // Salva os dados completos no localStorage
          localStorage.setItem('instagram_auth', JSON.stringify({
            user: userData,
            tokens: parsedData.tokens,
            scopes: parsedData.scopes,
            authenticated_at: parsedData.authenticated_at
          }));
        } catch (error) {
          console.error('Erro ao processar dados do Instagram:', error);
        }
      }

      setAuthState({
        isAuthenticated: true,
        user: userData,
        isLoading: false,
        error: null,
        authStatus: 'success'
      });

      // Se não temos dados completos do Instagram, salva pelo menos o básico
      if (!instagramData) {
        localStorage.setItem('instagram_auth', JSON.stringify({
          user: userData,
          tokens: null,
          scopes: [],
          authenticated_at: new Date().toISOString()
        }));
      }

      // Limpa os parâmetros da URL após processar
      if (typeof window !== 'undefined') {
        const url = new URL(window.location.href);
        url.searchParams.delete('instagram_auth');
        url.searchParams.delete('instagram_user');
        url.searchParams.delete('instagram_data');
        window.history.replaceState({}, '', url.toString());
      }
    } else if (authResult === 'error') {
      const decodedError = errorMessage ? decodeURIComponent(errorMessage) : 'Erro na autenticação do Instagram';
      
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: decodedError,
        authStatus: 'error'
      });

      // Limpa os parâmetros da URL após processar
      if (typeof window !== 'undefined') {
        const url = new URL(window.location.href);
        url.searchParams.delete('instagram_auth');
        url.searchParams.delete('error_message');
        window.history.replaceState({}, '', url.toString());
      }
    }

    // Verifica se há dados de autenticação salvos no localStorage
    if (typeof window !== 'undefined' && !authResult) {
      const savedAuthData = localStorage.getItem('instagram_auth');
      console.log('💾 Verificando localStorage:', { hasSavedData: !!savedAuthData });
      
      if (savedAuthData) {
        try {
          const parsedData = JSON.parse(savedAuthData);
          console.log('✅ Dados recuperados do localStorage:', { 
            hasUser: !!parsedData.user, 
            username: parsedData.user?.username,
            hasTokens: !!parsedData.tokens 
          });
          
          setAuthState({
            isAuthenticated: true,
            user: parsedData.user,
            isLoading: false,
            error: null,
            authStatus: 'success'
          });
        } catch (error) {
          console.error('❌ Erro ao carregar dados de autenticação salvos:', error);
          localStorage.removeItem('instagram_auth');
        }
              } else {
          console.log('❌ Nenhum dado de autenticação encontrado no localStorage');
          // Define isLoading como false quando não há dados salvos
          setAuthState(prev => ({
            ...prev,
            isLoading: false
          }));
        }
      } else if (!authResult) {
        // Se não está no browser ou já processou authResult, não está mais carregando
        setAuthState(prev => ({
          ...prev,
          isLoading: false
        }));
      }
  }, [searchParams]);

  const logout = () => {
    setAuthState({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      error: null,
      authStatus: 'idle'
    });

    // Remove dados salvos
    if (typeof window !== 'undefined') {
      localStorage.removeItem('instagram_auth');
    }
  };

  const clearError = () => {
    setAuthState(prev => ({
      ...prev,
      error: null,
      authStatus: prev.isAuthenticated ? 'success' : 'idle'
    }));
  };

  return {
    ...authState,
    logout,
    clearError
  };
} 