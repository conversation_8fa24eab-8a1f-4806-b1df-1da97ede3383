import axios from 'axios';

/**
 * Classe para interagir com o Instagram Graph API
 * Implementação baseada no analytics-connect funcional
 */
export class InstagramGraphAPI {
  private appId: string;
  private appSecret: string;
  private redirectUri: string;
  private graphApiVersion: string;
  private baseUrl: string;
  private debugMode: boolean;

  constructor() {
    this.appId = process.env.INSTAGRAM_APP_ID || '';
    this.appSecret = process.env.INSTAGRAM_APP_SECRET || '';
    this.redirectUri = process.env.INSTAGRAM_REDIRECT_URI || '';
    this.graphApiVersion = process.env.GRAPH_API_VERSION || 'v19.0';
    this.baseUrl = `https://graph.facebook.com/${this.graphApiVersion}`;
    this.debugMode = process.env.DEBUG_MODE === 'true';
    
    if (!this.appId || !this.appSecret) {
      console.error('❌ Variáveis de ambiente não carregadas:');
      console.error('   INSTAGRAM_APP_ID:', this.appId ? 'CONFIGURADO' : 'UNDEFINED');
      console.error('   INSTAGRAM_APP_SECRET:', this.appSecret ? 'CONFIGURADO' : 'UNDEFINED');
      console.error('   INSTAGRAM_REDIRECT_URI:', this.redirectUri || 'UNDEFINED');
      throw new Error('Instagram App ID e App Secret são obrigatórios');
    }
    
    if (this.debugMode) {
      console.log('✅ Instagram API configurado:');
      console.log('   App ID:', this.appId.substring(0, 10) + '...');
      console.log('   Redirect URI:', this.redirectUri);
      console.log('   Graph API Version:', this.graphApiVersion);
    }
  }

  /**
   * Gera a URL de autorização para o usuário (Instagram Login direto)
   * @param scopes - Permissões solicitadas (novos scopes do Instagram API)
   * @param state - Estado para validação de segurança
   * @returns URL de autorização
   */
  getAuthorizationUrl(
    scopes: string[] = [
      'instagram_business_basic',           // Acesso básico ao perfil profissional
      'instagram_business_content_publish', // Publicar conteúdo
      'instagram_business_manage_messages', // Gerenciar mensagens
      'instagram_business_manage_comments', // Gerenciar comentários
      'instagram_business_manage_insights'  // Insights e analytics (ESSENCIAL!)
    ], 
    state: string | null = null
  ): string {
    const scope = scopes.join(',');
    const params = new URLSearchParams({
      client_id: this.appId,
      redirect_uri: this.redirectUri,
      scope: scope,
      response_type: 'code'
    });

    if (state) {
      params.append('state', state);
    }

    const authUrl = `https://www.instagram.com/oauth/authorize?${params.toString()}`;
    
    if (this.debugMode) {
      console.log('🔗 URL de autorização gerada (Instagram direto):', authUrl);
    }

    return authUrl;
  }

  /**
   * Troca o código de autorização por um token de acesso (Instagram API direto)
   * @param code - Código de autorização recebido
   * @returns Dados do token de acesso
   */
  async exchangeCodeForToken(code: string): Promise<any> {
    try {
      const url = 'https://api.instagram.com/oauth/access_token';

      if (this.debugMode) {
        console.log('🔄 Trocando código por token (Instagram API)...', { code: code.substring(0, 10) + '...' });
        console.log('🔧 Parâmetros da requisição:', {
          client_id: this.appId ? this.appId.substring(0, 10) + '...' : 'UNDEFINED',
          client_secret: this.appSecret ? 'CONFIGURADO' : 'UNDEFINED',
          redirect_uri: this.redirectUri,
          grant_type: 'authorization_code'
        });
      }

      // Instagram API espera form-data, não JSON
      const formData = new URLSearchParams();
      formData.append('client_id', this.appId);
      formData.append('client_secret', this.appSecret);
      formData.append('grant_type', 'authorization_code');
      formData.append('redirect_uri', this.redirectUri);
      formData.append('code', code);

      const response = await axios.post(url, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      if (this.debugMode) {
        console.log('✅ Token obtido com sucesso:', {
          access_token: response.data.access_token ? 'OBTIDO' : 'NÃO OBTIDO',
          user_id: response.data.user_id || 'NÃO FORNECIDO'
        });
      }

      return response.data;

    } catch (error: any) {
      console.error('❌ Erro ao trocar código por token:', error.response?.data || error.message);
      throw new Error(`Falha na troca do código por token: ${error.response?.data?.error_message || error.message}`);
    }
  }

  /**
   * Converte token de curta duração em token de longa duração (60 dias)
   * @param shortLivedToken - Token de curta duração
   * @returns Token de longa duração
   */
  async exchangeForLongLivedToken(shortLivedToken: string): Promise<any> {
    try {
      const url = 'https://graph.instagram.com/access_token';
      const params = {
        grant_type: 'ig_exchange_token',
        client_secret: this.appSecret,
        access_token: shortLivedToken
      };

      if (this.debugMode) {
        console.log('🔄 Convertendo para token de longa duração...');
      }

      const response = await axios.get(url, { params });

      if (this.debugMode) {
        console.log('✅ Token de longa duração obtido:', {
          access_token: response.data.access_token ? 'OBTIDO' : 'NÃO OBTIDO',
          expires_in: response.data.expires_in || 'NÃO FORNECIDO'
        });
      }

      return response.data;

    } catch (error: any) {
      console.error('❌ Erro ao converter token:', error.response?.data || error.message);
      throw new Error(`Falha na conversão do token: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Obtém informações do perfil do usuário
   * @param accessToken - Token de acesso
   * @returns Dados do perfil
   */
  async getUserProfile(accessToken: string): Promise<any> {
    try {
      const url = 'https://graph.instagram.com/me';
      const params = {
        fields: 'id,username,name,profile_picture_url,followers_count,follows_count,media_count',
        access_token: accessToken
      };

      if (this.debugMode) {
        console.log('👤 Obtendo perfil do usuário...');
        console.log('🔧 URL:', url);
        console.log('🔧 Token:', accessToken ? accessToken.substring(0, 20) + '...' : 'UNDEFINED');
      }

      const response = await axios.get(url, { params });

      if (this.debugMode) {
        console.log('✅ Perfil obtido:', {
          id: response.data.id || 'NÃO FORNECIDO',
          username: response.data.username || 'NÃO FORNECIDO',
          account_type: response.data.account_type || 'NÃO FORNECIDO'
        });
      }

      return response.data;

    } catch (error: any) {
      console.error('❌ Erro ao obter perfil:', error.response?.data || error.message);
      throw new Error(`Falha ao obter perfil: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Valida se um token ainda está válido
   * @param accessToken - Token de acesso
   * @returns Informações sobre a validade do token
   */
  async validateToken(accessToken: string): Promise<any> {
    try {
      const url = `${this.baseUrl}/debug_token`;
      const params = {
        input_token: accessToken,
        access_token: `${this.appId}|${this.appSecret}`
      };

      if (this.debugMode) {
        console.log('🔍 Validando token...');
      }

      const response = await axios.get(url, { params });

      if (this.debugMode) {
        console.log('✅ Token validado:', {
          is_valid: response.data.data?.is_valid || false,
          expires_at: response.data.data?.expires_at || 'NÃO FORNECIDO',
          app_id: response.data.data?.app_id || 'NÃO FORNECIDO'
        });
      }

      return response.data;

    } catch (error: any) {
      console.error('❌ Erro ao validar token:', error.response?.data || error.message);
      throw new Error(`Falha na validação do token: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * === INSTAGRAM INSIGHTS API ===
   * Extração de métricas e insights conforme documentação Meta 2024
   */

  /**
   * Obtém insights de uma mídia específica
   */
  async getMediaInsights(mediaId: string, accessToken: string, metrics?: string[], breakdown?: string) {
    try {
      console.log(`📊 Obtendo insights da mídia: ${mediaId}`);

      // Métricas padrão baseadas na documentação Meta 2024
      const defaultMetrics = [
        'views',           // Nova métrica principal
        'reach',           // Usuários únicos que viram
        'likes',           // Curtidas
        'comments',        // Comentários
        'shares',          // Compartilhamentos
        'saved'            // Salvamentos
      ];

      const metricsToUse = metrics || defaultMetrics;
      
      const params: any = {
        metric: metricsToUse.join(','),
        access_token: accessToken
      };

      // Adiciona breakdown se especificado
      if (breakdown) {
        params.breakdown = breakdown;
      }

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${mediaId}/insights`,
        { params }
      );

      console.log(`✅ Insights obtidos para mídia ${mediaId}`);
      return this.processInsightsResponse(response.data);

    } catch (error: any) {
      console.error('❌ Erro ao obter insights da mídia:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Obtém insights do usuário (perfil)
   */
  async getUserInsights(userId: string, accessToken: string, metrics?: string[], period?: string) {
    try {
      console.log(`👤 Obtendo insights do usuário: ${userId}`);

      // Métricas padrão para insights de usuário (baseado na resposta do Instagram API)
      const defaultMetrics = [
        'reach',                    // Alcance total
        'profile_views',           // Visualizações do perfil
        'accounts_engaged',        // Contas que interagiram
        'follower_count',          // Contagem de seguidores
        'website_clicks'           // Cliques no site
      ];

      const metricsToUse = metrics || defaultMetrics;
      const periodToUse = period || 'day';

      const params = {
        metric: metricsToUse.join(','),
        period: periodToUse,
        access_token: accessToken
      };

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${userId}/insights`,
        { params }
      );

      console.log(`✅ Insights do usuário obtidos para ${userId}`);
      return this.processInsightsResponse(response.data);

    } catch (error: any) {
      console.error('❌ Erro ao obter insights do usuário:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Obtém insights de Stories (disponível apenas por 24h)
   */
  async getStoryInsights(storyId: string, accessToken: string) {
    try {
      console.log(`📖 Obtendo insights da story: ${storyId}`);

      // Métricas específicas para Stories
      const storyMetrics = [
        'views',                          // Visualizações
        'reach',                          // Alcance
        'replies',                        // Respostas
        'shares'                          // Compartilhamentos
      ];

      const params = {
        metric: storyMetrics.join(','),
        access_token: accessToken
      };

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${storyId}/insights`,
        { params }
      );

      console.log(`✅ Insights da story obtidos para ${storyId}`);
      return this.processInsightsResponse(response.data);

    } catch (error: any) {
      console.error('❌ Erro ao obter insights da story:', error.response?.data || error.message);
      
      // Stories podem ter erro se tiverem menos de 5 visualizações ou expiraram
      if (error.response?.data?.error?.code === 10) {
        console.log('⚠️ Story não tem dados suficientes (menos de 5 visualizações)');
        return { error: 'insufficient_data', message: 'Story com menos de 5 visualizações' };
      }
      
      throw error;
    }
  }

  /**
   * Obtém insights de Reels com métricas específicas
   */
  async getReelsInsights(reelId: string, accessToken: string) {
    try {
      console.log(`🎬 Obtendo insights do reel: ${reelId}`);

      // Métricas específicas para Reels
      const reelsMetrics = [
        'views',                          // Visualizações (nova métrica)
        'reach',                          // Alcance
        'likes',                          // Curtidas
        'comments',                       // Comentários
        'shares',                         // Compartilhamentos
        'saved',                          // Salvamentos
        'ig_reels_avg_watch_time',       // Tempo médio assistido
        'ig_reels_video_view_total_time'  // Tempo total assistido
      ];

      const params = {
        metric: reelsMetrics.join(','),
        access_token: accessToken
      };

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${reelId}/insights`,
        { params }
      );

      console.log(`✅ Insights do reel obtidos para ${reelId}`);
      return this.processInsightsResponse(response.data);

    } catch (error: any) {
      console.error('❌ Erro ao obter insights do reel:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Obtém insights com breakdown detalhado
   */
  async getDetailedInsights(mediaId: string, accessToken: string, metric: string, breakdown: string) {
    try {
      console.log(`🔍 Obtendo insights detalhados: ${metric} com breakdown ${breakdown}`);

      const params = {
        metric: metric,
        breakdown: breakdown,
        access_token: accessToken
      };

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${mediaId}/insights`,
        { params }
      );

      console.log(`✅ Insights detalhados obtidos`);
      return this.processInsightsResponse(response.data);

    } catch (error: any) {
      console.error('❌ Erro ao obter insights detalhados:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Obtém insights históricos do usuário com período personalizado
   */
  async getHistoricalUserInsights(userId: string, accessToken: string, period: 'day' | 'week' | 'days_28' | 'month' | 'lifetime') {
    try {
      console.log(`📈 Obtendo insights históricos: período ${period}`);

      const metrics = [
        'accounts_engaged',
        'profile_views',
        'website_clicks',
        'follower_count'
      ];

      const params = {
        metric: metrics.join(','),
        period: period,
        access_token: accessToken
      };

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${userId}/insights`,
        { params }
      );

      console.log(`✅ Insights históricos obtidos para período ${period}`);
      return this.processInsightsResponse(response.data);

    } catch (error: any) {
      console.error('❌ Erro ao obter insights históricos:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Processa e normaliza a resposta de insights da API
   */
  private processInsightsResponse(data: any) {
    if (!data || !data.data) {
      return { metrics: [], summary: {} };
    }

    const metrics = data.data.map((metric: any) => ({
      name: metric.name,
      title: metric.title,
      description: metric.description,
      value: metric.values?.[0]?.value || 0,
      period: metric.period,
      total_value: metric.total_value?.value || 0,
      breakdowns: metric.total_value?.breakdowns || []
    }));

    // Cria um resumo das métricas principais
    const summary = metrics.reduce((acc: any, metric: any) => {
      acc[metric.name] = metric.value;
      return acc;
    }, {});

    return {
      metrics,
      summary,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Obtém insights completos de uma mídia (detecção automática do tipo)
   */
  async getCompleteMediaInsights(mediaId: string, accessToken: string) {
    try {
      console.log(`🎯 Obtendo insights completos da mídia: ${mediaId}`);

      // Primeiro, obtém informações da mídia para determinar o tipo
      const mediaInfo = await this.getMediaInfo(mediaId, accessToken);
      const mediaType = mediaInfo.media_type;

      let insights;

      switch (mediaType) {
        case 'IMAGE':
        case 'CAROUSEL_ALBUM':
          // Métricas para posts de imagem/carrossel
          insights = await this.getMediaInsights(mediaId, accessToken, [
            'views', 'reach', 'likes', 'comments', 'shares', 'saved'
          ]);
          break;

        case 'VIDEO':
          // Para vídeos, verifica se é um Reel
          if (mediaInfo.permalink && mediaInfo.permalink.includes('/reel/')) {
            insights = await this.getReelsInsights(mediaId, accessToken);
          } else {
            // Vídeo normal no feed
            insights = await this.getMediaInsights(mediaId, accessToken);
          }
          break;

        default:
          insights = await this.getMediaInsights(mediaId, accessToken);
      }

      return {
        media_info: mediaInfo,
        insights: insights
      };

    } catch (error: any) {
      console.error('❌ Erro ao obter insights completos:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Obtém informações básicas de uma mídia
   */
  async getMediaInfo(mediaId: string, accessToken: string) {
    try {
      const params = {
        fields: 'id,media_type,media_url,permalink,timestamp,caption',
        access_token: accessToken
      };

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${mediaId}`,
        { params }
      );

      return response.data;

    } catch (error: any) {
      console.error('❌ Erro ao obter informações da mídia:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Obtém insights de todas as mídias recentes do usuário
   */
  async getAllRecentMediaInsights(userId: string, accessToken: string, limit: number = 10) {
    try {
      console.log(`📱 Obtendo insights de todas as mídias recentes (limite: ${limit})`);

      // Primeiro, obtém as mídias recentes do usuário
      const userProfile = await this.getUserProfile(accessToken);
      const userId = userProfile.id;
      
      const params = {
        fields: 'id,media_type,media_url,permalink,timestamp,caption',
        limit: limit,
        access_token: accessToken
      };

      const response = await axios.get(
        `https://graph.instagram.com/v23.0/${userId}/media`,
        { params }
      );

      const mediaList = response.data;

      if (!mediaList.data || mediaList.data.length === 0) {
        return { media_insights: [], summary: {} };
      }

      // Obtém insights para cada mídia
      const allInsights = [];
      for (const media of mediaList.data) {
        try {
          const insights = await this.getCompleteMediaInsights(media.id, accessToken);
          allInsights.push({
            media_id: media.id,
            ...insights
          });

          // Delay para evitar rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.warn(`⚠️ Erro ao obter insights da mídia ${media.id}:`, error);
          // Continua com as outras mídias
        }
      }

      // Calcula métricas agregadas
      const aggregatedMetrics = this.aggregateInsights(allInsights);

      return {
        media_insights: allInsights,
        aggregated_metrics: aggregatedMetrics,
        total_media: allInsights.length,
        timestamp: new Date().toISOString()
      };

    } catch (error: any) {
      console.error('❌ Erro ao obter insights de todas as mídias:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Agrega insights de múltiplas mídias
   */
  private aggregateInsights(allInsights: any[]) {
    const aggregated: any = {
      total_views: 0,
      total_reach: 0,
      total_likes: 0,
      total_comments: 0,
      total_shares: 0,
      total_saved: 0,
      total_interactions: 0,
      average_engagement_rate: 0
    };

    let validInsights = 0;

    for (const mediaInsight of allInsights) {
      if (mediaInsight.insights && mediaInsight.insights.summary) {
        const summary = mediaInsight.insights.summary;
        
        aggregated.total_views += summary.views || 0;
        aggregated.total_reach += summary.reach || 0;
        aggregated.total_likes += summary.likes || 0;
        aggregated.total_comments += summary.comments || 0;
        aggregated.total_shares += summary.shares || 0;
        aggregated.total_saved += summary.saved || 0;
        // Calcular interações totais manualmente
        const interactions = (summary.likes || 0) + (summary.comments || 0) + (summary.shares || 0) + (summary.saved || 0);
        aggregated.total_interactions += interactions;
        
        validInsights++;
      }
    }

    // Calcula taxa de engajamento média
    if (validInsights > 0 && aggregated.total_reach > 0) {
      aggregated.average_engagement_rate = (
        (aggregated.total_likes + aggregated.total_comments + aggregated.total_shares) / 
        aggregated.total_reach * 100
      ).toFixed(2);
    }

    return aggregated;
  }

  /**
   * Diagnóstico completo da conta
   */
  async diagnoseAccount(accessToken: string) {
    console.log('🏥 Iniciando diagnóstico completo da conta...');
    
    try {
      // 1. Obter perfil básico
      const profile = await this.getUserProfile(accessToken);
      console.log(`👤 Perfil: @${profile.username} (${profile.followers_count} seguidores)`);
      
      // 2. Verificar se tem conteúdo
      const hasContent = profile.media_count > 0;
      console.log(`📱 Conteúdo: ${hasContent ? `${profile.media_count} posts` : 'Sem posts'}`);
      
      // 3. Recomendações baseadas no diagnóstico
      const recommendations = [];
      
      if (!hasContent) {
        recommendations.push('📝 Publique seu primeiro post para acessar métricas de engajamento');
        recommendations.push('📊 Métricas de reach/impressões só funcionam com conteúdo');
      }
      
      if (profile.followers_count < 100) {
        recommendations.push('👥 Contas com poucos seguidores têm limitações em algumas métricas');
      }

      return {
        profile,
        content_status: {
          has_posts: hasContent,
          media_count: profile.media_count,
          followers_count: profile.followers_count
        },
        recommendations,
        overall_health: hasContent ? 'healthy' : 'limited'
      };

    } catch (error: any) {
      console.error('❌ Erro no diagnóstico:', error);
      throw new Error(`Diagnóstico falhou: ${error.message}`);
    }
  }
}

// Instância única para uso em toda a aplicação
export const instagramAPI = new InstagramGraphAPI(); 