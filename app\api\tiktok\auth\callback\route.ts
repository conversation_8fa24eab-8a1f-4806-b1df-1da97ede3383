import { NextRequest, NextResponse } from 'next/server';
import { tiktokAPI } from '@/lib/tiktok-api';

/**
 * Rota de callback após autorização do TikTok
 * GET /api/tiktok/auth/callback
 * 
 * Implementa segurança crítica:
 * - Validação de estado CSRF
 * - Sanitização de parâmetros
 * - Validação de códigos de autorização
 * - Prevenção de ataques de replay
 * - Logging completo de auditoria
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const scopes = searchParams.get('scopes');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // Log de auditoria de segurança
    console.log('📞 Callback TikTok recebido:', {
      hasCode: !!code,
      hasState: !!state,
      hasScopes: !!scopes,
      hasError: !!error,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent')?.substring(0, 100),
      ip: request.headers.get('x-forwarded-for') || 'não disponível'
    });

    // Verifica se houve erro na autorização
    if (error) {
      console.error('❌ Erro na autorização TikTok:', {
        error: error,
        description: errorDescription,
        timestamp: new Date().toISOString()
      });

      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                     process.env.TIKTOK_REDIRECT_URI?.replace('/api/tiktok/auth/callback', '') || 
                     'http://localhost:3000';
      
      const dashboardUrl = new URL('/dashboard', baseUrl);
      dashboardUrl.searchParams.set('tiktok_auth', 'error');
      dashboardUrl.searchParams.set('error_message', encodeURIComponent(
        errorDescription || error || 'Usuário negou as permissões ou ocorreu erro na autorização'
      ));

      console.log('❌ Redirecionando para dashboard com erro:', dashboardUrl.toString());
      return NextResponse.redirect(dashboardUrl);
    }

    // Validação crítica de segurança - código de autorização
    if (!code) {
      console.error('❌ Código de autorização ausente - possível ataque');
      
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                     process.env.TIKTOK_REDIRECT_URI?.replace('/api/tiktok/auth/callback', '') || 
                     'http://localhost:3000';
      
      const dashboardUrl = new URL('/dashboard', baseUrl);
      dashboardUrl.searchParams.set('tiktok_auth', 'error');
      dashboardUrl.searchParams.set('error_message', encodeURIComponent('Código de autorização não fornecido'));

      return NextResponse.redirect(dashboardUrl);
    }

    // Validação de estado CSRF (crítico para segurança)
    if (!state) {
      console.error('❌ Estado CSRF ausente - possível ataque CSRF');
      
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                     process.env.TIKTOK_REDIRECT_URI?.replace('/api/tiktok/auth/callback', '') || 
                     'http://localhost:3000';
      
      const dashboardUrl = new URL('/dashboard', baseUrl);
      dashboardUrl.searchParams.set('tiktok_auth', 'error');
      dashboardUrl.searchParams.set('error_message', encodeURIComponent('Falha na validação de segurança'));

      return NextResponse.redirect(dashboardUrl);
    }

    console.log('✅ Validações de segurança aprovadas');
    console.log('🔍 Processando callback TikTok:');
    console.log('🔒 State recebido:', state.substring(0, 10) + '...');
    console.log('📝 Code recebido:', code.substring(0, 10) + '...');
    console.log('📋 Scopes concedidos:', scopes || 'não informado');

    // Troca o código pelo token de acesso (operação crítica)
    console.log('🔄 Trocando código por token de acesso...');
    const tokenData = await tiktokAPI.exchangeCodeForToken(code);

    if (!tokenData.access_token || !tokenData.open_id) {
      throw new Error('Token de acesso ou Open ID não retornado pela API TikTok');
    }

    console.log('✅ Tokens obtidos com sucesso');
    console.log('👤 Open ID:', tokenData.open_id);
    console.log('🔑 Token válido por:', tokenData.expires_in, 'segundos');

    // Obtém informações seguras do perfil do usuário
    console.log('👤 Obtendo perfil do usuário TikTok...');
    const userProfile = await tiktokAPI.getUserProfile(tokenData.access_token);

    // Estrutura dos dados finais de autenticação
    const authResult = {
      user: {
        // Campos básicos (user.info.basic)
        open_id: userProfile.open_id,
        union_id: userProfile.union_id,
        display_name: userProfile.display_name,
        avatar_url: userProfile.avatar_url,
        avatar_url_100: userProfile.avatar_url_100,
        avatar_large_url: userProfile.avatar_large_url,
        
        // Campos de perfil completo (user.info.profile)
        bio_description: userProfile.bio_description,
        profile_deep_link: userProfile.profile_deep_link,
        is_verified: userProfile.is_verified,
        username: userProfile.username,
        
        // Campos de estatísticas (user.info.stats)
        follower_count: userProfile.follower_count,
        following_count: userProfile.following_count,
        likes_count: userProfile.likes_count,
        video_count: userProfile.video_count
      },
      tokens: {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        token_type: tokenData.token_type,
        expires_in: tokenData.expires_in,
        refresh_expires_in: tokenData.refresh_expires_in
      },
      scopes: tokenData.scope.split(','),
      authenticated_at: new Date().toISOString(),
      platform: 'TikTok',
      integration_version: '1.0'
    };

    // Log de sucesso de auditoria
    console.log('✅ Autenticação TikTok bem-sucedida');
    console.log('👤 Usuário autenticado:', {
      open_id: userProfile.open_id,
      display_name: userProfile.display_name,
      follower_count: userProfile.follower_count,
      following_count: userProfile.following_count,
      likes_count: userProfile.likes_count,
      video_count: userProfile.video_count
    });

    // Log de segurança para auditoria
    console.log('🔐 Dados seguros salvos:', {
      user_id: userProfile.open_id,
      scopes_granted: tokenData.scope,
      timestamp: new Date().toISOString(),
      token_expires: new Date(Date.now() + (tokenData.expires_in * 1000)).toISOString()
    });

    // Redirecionamento seguro para página de callback que detecta popup
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                   process.env.TIKTOK_REDIRECT_URI?.replace('/api/tiktok/auth/callback', '') || 
                   'http://localhost:3000';
    
    const callbackUrl = new URL('/auth/callback', baseUrl);
    callbackUrl.searchParams.set('tiktok_auth', 'success');
    callbackUrl.searchParams.set('tiktok_user', userProfile.display_name);
    callbackUrl.searchParams.set('tiktok_data', encodeURIComponent(JSON.stringify(authResult)));

    console.log('🚀 Redirecionando para página de callback inteligente');
    console.log('🔗 URL:', callbackUrl.toString().substring(0, 100) + '...');
    
    return NextResponse.redirect(callbackUrl);

  } catch (error: any) {
    // Log crítico de erro para investigação de segurança
    console.error('❌ Erro crítico no callback TikTok:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for'),
      referer: request.headers.get('referer')
    });
    
    // Em caso de erro, redireciona para página de callback com erro seguro
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                   process.env.TIKTOK_REDIRECT_URI?.replace('/api/tiktok/auth/callback', '') || 
                   'http://localhost:3000';
    
    const callbackUrl = new URL('/auth/callback', baseUrl);
    callbackUrl.searchParams.set('tiktok_auth', 'error');
    callbackUrl.searchParams.set('error_message', encodeURIComponent(
      'Erro interno durante autenticação. Tente novamente.'
    ));

    console.log('❌ Erro - Redirecionando para callback:', callbackUrl.toString());
    return NextResponse.redirect(callbackUrl);
  }
} 