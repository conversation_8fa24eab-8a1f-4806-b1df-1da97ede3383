@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @font-face {
    font-family: "Apple Color Emoji";
    src: url("/AppleColorEmoji.ttf") format("truetype");
    unicode-range: U+1F600-1F64F, U+1F300-1F5FF, U+1F680-1F6FF, U+1F1E0-1F1FF, U+2600-26FF, U+2700-27BF, U+1F900-1F9FF, U+FE0E-FE0F, U+200D;
    font-display: swap;
  }

  * {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif;
  }

  /* Configuração para exibir emojis Apple corretamente */
  * {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Apple Color Emoji", system-ui, sans-serif;
  }
  .theme {
    --font-sans: var(--font-sans), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-mono: var(--font-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  }
  :root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.141 0.005 285.823);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.141 0.005 285.823);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.141 0.005 285.823);
    --primary: oklch(0.606 0.25 292.717);
    --primary-foreground: oklch(0.979 0.021 166.113);
    --secondary: oklch(0.967 0.001 286.375);
    --secondary-foreground: oklch(0.183 0.006 285.79);
    --muted: oklch(0.967 0.001 286.375);
    --muted-foreground: oklch(0.552 0.016 285.938);
    --accent: oklch(0.967 0.001 286.375);
    --accent-foreground: oklch(0.183 0.006 285.79);
    --destructive: oklch(0.637 0.237 25.331);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.92 0.004 286.32);
    --input: oklch(0.871 0.006 286.286);
    --ring: oklch(0.871 0.006 286.286);
    --chart-1: oklch(0.606 0.25 292.717);
    --chart-2: oklch(0.541 0.281 293.009);
    --chart-3: oklch(0.92 0.004 286.32);
    --chart-4: oklch(0.645 0.246 16.439);
    --chart-5: oklch(0.586 0.253 17.585);
    --radius: 0.625rem;
    --chart-6: oklch(0.696 0.17 162.48);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.37 0.013 285.805);
    --sidebar-primary: oklch(0.606 0.25 292.717);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.967 0.001 286.375);
    --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
    --sidebar-border: oklch(0.92 0.004 286.32);
    --sidebar-ring: oklch(0.871 0.006 286.286);
  }
  .dark {
    --background: oklch(0.183 0.006 285.79);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.183 0.006 285.79);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.183 0.006 285.79);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.606 0.25 292.717);
    --primary-foreground: oklch(0.979 0.021 166.113);
    --secondary: oklch(0.274 0.006 286.033);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.21 0.006 285.885);
    --muted-foreground: oklch(0.705 0.015 286.067);
    --accent: oklch(0.21 0.006 285.885);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.637 0.237 25.331);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.246 0.009 285.69);
    --input: oklch(0.246 0.009 285.69);
    --ring: oklch(0.442 0.017 285.786);
    --chart-1: oklch(0.606 0.25 292.717);
    --chart-2: oklch(0.541 0.281 293.009);
    --chart-3: oklch(0.274 0.006 286.033);
    --chart-4: oklch(0.645 0.246 16.439);
    --chart-5: oklch(0.586 0.253 17.585);
    --chart-6: oklch(0.696 0.17 162.48);
    --sidebar: oklch(0.21 0.006 285.885);
    --sidebar-foreground: oklch(0.967 0.001 286.375);
    --sidebar-primary: oklch(0.606 0.25 292.717);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.274 0.006 286.033);
    --sidebar-accent-foreground: oklch(0.967 0.001 286.375);
    --sidebar-border: oklch(0.274 0.006 286.033);
    --sidebar-ring: oklch(0.442 0.017 285.786);
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}