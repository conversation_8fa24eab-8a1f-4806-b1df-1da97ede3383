/**
 * TikTok for Business API Library
 * 
 * Esta biblioteca fornece funcionalidades para integração com a TikTok API,
 * incluindo autenticação OAuth 2.0, gestão de tokens e acesso a dados do usuário.
 * 
 * Implementa as melhores práticas de segurança:
 * - Validação rigorosa de parâmetros
 * - Sanitização de dados
 * - Proteção contra CSRF com estado único
 * - Gestão segura de tokens
 * - Rate limiting e tratamento de erros
 * 
 * @see https://developers.tiktok.com/doc/overview/
 */

interface TikTokAuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  refresh_expires_in: number;
  scope: string;
  open_id: string;
}

interface TikTokUserProfile {
  // Campos básicos (user.info.basic)
  open_id: string;
  union_id?: string;
  avatar_url?: string;
  avatar_url_100?: string;
  avatar_large_url?: string;
  display_name: string;
  
  // Campos de perfil completo (user.info.profile)
  bio_description?: string;
  profile_deep_link?: string;
  is_verified?: boolean;
  username?: string;
  
  // Campos de estatísticas (user.info.stats)
  follower_count?: number;
  following_count?: number;
  likes_count?: number;
  video_count?: number;
}

interface TikTokErrorResponse {
  error: string;
  error_description: string;
  log_id: string;
}

class TikTokAPI {
  private readonly clientKey: string;
  private readonly clientSecret: string;
  private readonly redirectUri: string;
  private readonly baseAuthUrl = 'https://www.tiktok.com/v2/auth/authorize/';
  private readonly baseApiUrl = 'https://open.tiktokapis.com/v2';

  constructor() {
    this.clientKey = process.env.TIKTOK_CLIENT_KEY || '';
    this.clientSecret = process.env.TIKTOK_CLIENT_SECRET || '';
    this.redirectUri = process.env.TIKTOK_REDIRECT_URI || '';

    // Validação de configuração crítica para segurança
    if (!this.clientKey || !this.clientSecret || !this.redirectUri) {
      throw new Error('❌ Configurações do TikTok incompletas. Verifique as variáveis de ambiente TIKTOK_CLIENT_KEY, TIKTOK_CLIENT_SECRET e TIKTOK_REDIRECT_URI');
    }

    console.log('✅ TikTok API inicializada com sucesso');
    console.log('🔑 Client Key:', this.clientKey.substring(0, 10) + '...');
    console.log('🔗 Redirect URI:', this.redirectUri);
  }

  /**
   * Gera URL de autorização OAuth 2.0 para o TikTok
   * 
   * @param scopes - Lista de permissões solicitadas
   * @param state - Token CSRF único para prevenção de ataques
   * @returns URL de autorização completa
   */
  getAuthorizationUrl(scopes: string[], state: string): string {
    try {
      // Validação de segurança dos parâmetros
      if (!Array.isArray(scopes) || scopes.length === 0) {
        throw new Error('Scopes são obrigatórios e devem ser um array não vazio');
      }

      if (!state || state.length < 10) {
        throw new Error('Estado CSRF deve ter pelo menos 10 caracteres');
      }

      // Sanitização e validação das permissões
      const validScopes = this.validateScopes(scopes);
      const scopeString = validScopes.join(',');

      // Construção segura da URL
      const authUrl = new URL(this.baseAuthUrl);
      const params = new URLSearchParams({
        client_key: this.clientKey,
        scope: scopeString,
        response_type: 'code',
        redirect_uri: this.redirectUri,
        state: state,
        disable_auto_auth: '0' // Permite pular página de autorização para sessões válidas
      });

      authUrl.search = params.toString();

      console.log('🔗 URL de autorização TikTok gerada:');
      console.log('📋 Scopes solicitados:', validScopes);
      console.log('🔒 Estado CSRF:', state.substring(0, 10) + '...');

      return authUrl.toString();
    } catch (error) {
      console.error('❌ Erro ao gerar URL de autorização TikTok:', error);
      throw error;
    }
  }

  /**
   * Troca código de autorização por tokens de acesso
   * 
   * @param authCode - Código recebido do callback de autorização
   * @returns Tokens de acesso e informações do usuário
   */
  async exchangeCodeForToken(authCode: string): Promise<TikTokAuthTokens> {
    try {
      // Validação de segurança do código
      if (!authCode || authCode.length < 10) {
        throw new Error('Código de autorização inválido ou muito curto');
      }

      console.log('🔄 Trocando código por token de acesso TikTok...');
      console.log('📝 Código recebido:', authCode.substring(0, 10) + '...');

      const tokenUrl = `${this.baseApiUrl}/oauth/token/`;
      const body = new URLSearchParams({
        client_key: this.clientKey,
        client_secret: this.clientSecret,
        code: authCode,
        grant_type: 'authorization_code',
        redirect_uri: this.redirectUri
      });

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cache-Control': 'no-cache',
          'User-Agent': 'AFaceCompany-TikTokIntegration/1.0'
        },
        body: body.toString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Resposta HTTP não OK:', response.status, errorText);
        throw new Error(`Erro HTTP ${response.status}: ${errorText}`);
      }

      const tokenData = await response.json();

      // Verificação de resposta de erro da API
      if (tokenData.error) {
        console.error('❌ Erro da API TikTok:', tokenData);
        throw new Error(`Erro TikTok: ${tokenData.error} - ${tokenData.error_description}`);
      }

      // Validação da estrutura da resposta
      if (!tokenData.access_token || !tokenData.open_id) {
        console.error('❌ Resposta da API inválida:', tokenData);
        throw new Error('Resposta da API TikTok inválida - tokens ausentes');
      }

      console.log('✅ Tokens obtidos com sucesso');
      console.log('👤 Open ID:', tokenData.open_id);
      console.log('🔑 Token expira em:', tokenData.expires_in, 'segundos');
      console.log('🔄 Refresh token expira em:', tokenData.refresh_expires_in, 'segundos');

      return {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        token_type: tokenData.token_type || 'Bearer',
        expires_in: tokenData.expires_in || 86400, // 24 horas padrão
        refresh_expires_in: tokenData.refresh_expires_in || 31536000, // 365 dias padrão
        scope: tokenData.scope || 'user.info.basic',
        open_id: tokenData.open_id
      };

    } catch (error) {
      console.error('❌ Erro ao trocar código por token:', error);
      throw error;
    }
  }

  /**
   * Obtém informações do perfil do usuário
   * 
   * @param accessToken - Token de acesso válido
   * @returns Dados do perfil do usuário
   */
  async getUserProfile(accessToken: string): Promise<TikTokUserProfile> {
    try {
      // Validação de segurança do token
      if (!accessToken || accessToken.length < 10) {
        throw new Error('Token de acesso inválido ou ausente');
      }

      console.log('👤 Obtendo perfil do usuário TikTok...');

      // Corrige URL e método conforme documentação oficial - incluindo campos de perfil completo
      const fields = 'open_id,union_id,avatar_url,avatar_url_100,avatar_large_url,display_name,bio_description,profile_deep_link,is_verified,username,follower_count,following_count,likes_count,video_count';
      const profileUrl = `${this.baseApiUrl}/user/info/?fields=${fields}`;

      const response = await fetch(profileUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erro ao obter perfil:', response.status, errorText);
        throw new Error(`Erro HTTP ${response.status}: ${errorText}`);
      }

      const profileData = await response.json();

      // Verificação de erro na resposta (TikTok sempre retorna 'error', mas code: 'ok' = sucesso)
      if (profileData.error && profileData.error.code !== 'ok') {
        console.error('❌ Erro da API TikTok:', profileData.error);
        throw new Error(`Erro TikTok: ${profileData.error.message || profileData.error.code}`);
      }

      console.log('✅ Resposta da API TikTok:', {
        success: profileData.error?.code === 'ok',
        log_id: profileData.error?.log_id,
        hasUserData: !!profileData.data
      });

      // Log completo para debug (removendo dados sensíveis)
      console.log('🔍 Dados completos recebidos:', JSON.stringify(profileData, null, 2));

      const userInfo = profileData.data?.user || profileData.data;
      
      if (!userInfo || !userInfo.open_id) {
        console.error('❌ Dados do usuário inválidos:', profileData);
        throw new Error('Dados do usuário não encontrados na resposta');
      }

      console.log('✅ Perfil obtido com sucesso');
      console.log('👤 Usuário:', userInfo.display_name);
      console.log('🔗 Open ID:', userInfo.open_id);
      
      // Log das estatísticas para debug
      console.log('📊 Estatísticas disponíveis:', {
        follower_count: userInfo.follower_count,
        following_count: userInfo.following_count,
        likes_count: userInfo.likes_count,
        video_count: userInfo.video_count
      });

      return {
        // Campos básicos (user.info.basic)
        open_id: userInfo.open_id,
        union_id: userInfo.union_id,
        avatar_url: userInfo.avatar_url,
        avatar_url_100: userInfo.avatar_url_100,
        avatar_large_url: userInfo.avatar_large_url,
        display_name: userInfo.display_name || 'Usuário TikTok',
        
        // Campos de perfil completo (user.info.profile)
        bio_description: userInfo.bio_description,
        profile_deep_link: userInfo.profile_deep_link,
        is_verified: userInfo.is_verified,
        username: userInfo.username,
        
        // Campos de estatísticas (user.info.stats)
        follower_count: userInfo.follower_count,
        following_count: userInfo.following_count,
        likes_count: userInfo.likes_count,
        video_count: userInfo.video_count
      };

    } catch (error) {
      console.error('❌ Erro ao obter perfil do usuário:', error);
      throw error;
    }
  }

  /**
   * Renova token de acesso usando refresh token
   * 
   * @param refreshToken - Token de renovação válido
   * @returns Novos tokens de acesso
   */
  async refreshAccessToken(refreshToken: string): Promise<TikTokAuthTokens> {
    try {
      if (!refreshToken) {
        throw new Error('Refresh token é obrigatório');
      }

      console.log('🔄 Renovando token de acesso TikTok...');

      const tokenUrl = `${this.baseApiUrl}/oauth/token/`;
      const body = new URLSearchParams({
        client_key: this.clientKey,
        client_secret: this.clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token'
      });

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cache-Control': 'no-cache'
        },
        body: body.toString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erro HTTP ${response.status}: ${errorText}`);
      }

      const tokenData = await response.json();

      if (tokenData.error) {
        throw new Error(`Erro TikTok: ${tokenData.error} - ${tokenData.error_description}`);
      }

      console.log('✅ Token renovado com sucesso');
      return tokenData;

    } catch (error) {
      console.error('❌ Erro ao renovar token:', error);
      throw error;
    }
  }

  /**
   * Revoga token de acesso (logout)
   * 
   * @param accessToken - Token a ser revogado
   */
  async revokeToken(accessToken: string): Promise<void> {
    try {
      if (!accessToken) {
        throw new Error('Token de acesso é obrigatório');
      }

      console.log('🔒 Revogando token de acesso TikTok...');

      const revokeUrl = `${this.baseApiUrl}/oauth/revoke/`;
      const body = new URLSearchParams({
        client_key: this.clientKey,
        client_secret: this.clientSecret,
        token: accessToken
      });

      const response = await fetch(revokeUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cache-Control': 'no-cache'
        },
        body: body.toString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erro HTTP ${response.status}: ${errorText}`);
      }

      console.log('✅ Token revogado com sucesso');

    } catch (error) {
      console.error('❌ Erro ao revogar token:', error);
      throw error;
    }
  }

  /**
   * Valida e filtra scopes permitidos
   * 
   * @param scopes - Lista de scopes solicitados
   * @returns Lista de scopes válidos
   */
  private validateScopes(scopes: string[]): string[] {
    const validScopes = [
      'user.info.basic',           // Informações básicas do usuário (open id, avatar, display name) - Included in Login Kit
      'user.info.profile',         // Acesso a profile_web_link, profile_deep_link, bio_description, is_verified
      'user.info.stats',           // Dados estatísticos (likes count, follower count, following count, video count)
      'video.list'                 // Lista de vídeos do usuário
    ];

    const filteredScopes = scopes.filter(scope => {
      const isValid = validScopes.includes(scope);
      if (!isValid) {
        console.warn(`⚠️ Scope inválido ignorado: ${scope}`);
      }
      return isValid;
    });

    if (filteredScopes.length === 0) {
      console.log('📋 Nenhum scope válido fornecido, usando padrão: user.info.basic');
      return ['user.info.basic'];
    }

    return filteredScopes;
  }
}

// Instância singleton da API TikTok
export const tiktokAPI = new TikTokAPI(); 