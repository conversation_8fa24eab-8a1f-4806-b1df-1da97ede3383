import React, { useState } from 'react';
import Link from 'next/link';

export const Header: React.FC = () => {
  const [isProductOpen, setIsProductOpen] = useState(false);
  const [isSocialMediaOpen, setIsSocialMediaOpen] = useState(false);
  const [isMetrischoolOpen, setIsMetrischoolOpen] = useState(false);

  return (
    <header className="bg-white shadow-[0px_2px_5px_rgba(0,0,0,0.1)] w-full">
      <div className="bg-black bg-gradient-to-r from-[#fa12e3] to-[#ffd900] text-white text-base font-bold text-center leading-[1.3] px-5 py-[13px]">
        EXCLUSIVE: Master Social Media Management in 7 Days - Limited Spots Available!
      </div>
      
      <div className="max-w-[1241px] mx-auto flex items-center justify-between py-5 px-5">
        <div className="w-[184px]">
          <Link href="/" className="block">
            <img
              src="/logoo.svg"
              alt="Aface Company - Social Media management tool"
              className="aspect-[4.61] object-contain w-full h-[41px]"
            />
          </Link>
        </div>

        <nav className="flex-1 px-[25px]">
         {/*<ul className="flex items-center space-x-0 text-black font-medium">
            <li className="relative">
              <button
                className="flex items-center px-4 py-2.5 text-base leading-none hover:text-gray-600"
                onClick={() => setIsProductOpen(!isProductOpen)}
                onBlur={() => setTimeout(() => setIsProductOpen(false), 150)}
              >
                Product
                <span className="ml-2 text-[10px]">▼</span>
              </button>
              {isProductOpen && (
                <div className="absolute top-full left-0 bg-white shadow-lg border rounded-md py-2 z-10">
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Analytics</a>
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Scheduling</a>
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Reports</a>
                </div>
              )}
            </li>
            
            <li className="relative">
              <button
                className="flex items-center px-4 py-2.5 text-base leading-none hover:text-gray-600"
                onClick={() => setIsSocialMediaOpen(!isSocialMediaOpen)}
                onBlur={() => setTimeout(() => setIsSocialMediaOpen(false), 150)}
              >
                Social Media
                <span className="ml-2 text-[10px]">▼</span>
              </button>
              {isSocialMediaOpen && (
                <div className="absolute top-full left-0 bg-white shadow-lg border rounded-md py-2 z-10">
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Instagram</a>
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Facebook</a>
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Twitter</a>
                </div>
              )}
            </li>
            
            <li>
              <a href="#" className="block px-4 py-2.5 text-base leading-none hover:text-gray-600">
                Pricing
              </a>
            </li>
            
            <li>
              <a href="#" className="block px-4 py-2.5 text-base leading-none hover:text-gray-600">
                Agencies
              </a>
            </li>
            
            <li className="relative">
              <button
                className="flex items-center px-4 py-2.5 text-base leading-none hover:text-gray-600"
                onClick={() => setIsMetrischoolOpen(!isMetrischoolOpen)}
                onBlur={() => setTimeout(() => setIsMetrischoolOpen(false), 150)}
              >
                Metrischool
                <span className="ml-2 text-[10px]">▼</span>
              </button>
              {isMetrischoolOpen && (
                <div className="absolute top-full left-0 bg-white shadow-lg border rounded-md py-2 z-10">
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Courses</a>
                  <a href="#" className="block px-4 py-2 hover:bg-gray-100">Certifications</a>
                </div>
              )}
            </li>
          </ul>*/}  
        </nav>

        <div className="flex items-center space-x-3">
          <div className="bg-white rounded-lg px-1 py-0.5">
            <select className="text-black text-base font-normal uppercase leading-none bg-transparent border-none outline-none">
              <option value="en">EN</option>
              <option value="es">ES</option>
              <option value="fr">FR</option>
              <option value="de">DE</option>
            </select>
          </div>
          
          <a
            href="#"
            className="bg-black text-white px-5 py-[11px] rounded-lg text-base leading-none hover:bg-gray-800 transition-colors"
          >
          <span className="relative z-10">Sign up here</span>
          </a>
          
          <a
            href="#"
            className="relative bg-gray-100 text-black px-5 py-[11px] rounded-lg text-base leading-none hover:bg-gray-200 transition-colors"
          >
          <span className="relative z-10">Sign in</span>
            {/*<span className="relative z-10">Login</span>*/}
          </a>
        </div>
      </div>
    </header>
  );
};
