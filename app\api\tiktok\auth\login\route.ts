import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { tiktokAPI } from '@/lib/tiktok-api';

/**
 * Rota para iniciar o processo de autenticação com TikTok
 * GET /api/tiktok/auth/login
 * 
 * Implementa segurança robusta:
 * - Geração de estado CSRF único
 * - Validação de permissões
 * - Sanitização de parâmetros
 * - Logging de segurança
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Iniciando autenticação TikTok para AFace Company');
    
    // Gera estado CSRF seguro para prevenção de ataques
    const state = crypto.randomBytes(32).toString('hex');
    console.log('🔒 Estado CSRF gerado:', state.substring(0, 10) + '...');

    // Define as permissões necessárias do TikTok (scopes básicos que não requerem aprovação)
    const scopes = [
      'user.info.basic',     // Informações básicas (open id, avatar, display name) - Sempre disponível
      'user.info.profile',   // Perfil completo (bio, username, verificação, deep link) - Disponível
      'user.info.stats'      // Estatísticas do usuário (seguidores, curtidas, vídeos) - Disponível
    ];

    console.log('📋 Permissões solicitadas:', scopes);

    // Gera a URL de autorização usando a biblioteca segura
    const authUrl = tiktokAPI.getAuthorizationUrl(scopes, state);

    // Log de segurança para auditoria
    console.log('🔗 URL de autorização TikTok gerada com sucesso');
    console.log('⏰ Timestamp:', new Date().toISOString());
    console.log('🌐 User-Agent:', request.headers.get('user-agent')?.substring(0, 100));
    console.log('📍 IP do cliente:', request.headers.get('x-forwarded-for') || 'não disponível');

    // Verifica se é uma requisição para popup (parâmetro popup=true)
    const url = new URL(request.url);
    const isPopupRequest = url.searchParams.get('popup') === 'true';

    if (isPopupRequest) {
      // Resposta JSON para requisições de popup
      return NextResponse.json({
        success: true,
        message: 'URL de autenticação TikTok gerada com sucesso',
        authUrl: authUrl,
        state: state,
        scopes: scopes,
        security: {
          csrf_protection: true,
          secure_redirect: true,
          scope_validation: true
        },
        metadata: {
          platform: 'TikTok for Business',
          integration_version: '1.0',
          timestamp: new Date().toISOString()
        }
      });
    }

    // Resposta tradicional com redirecionamento direto para navegação normal
    return NextResponse.redirect(authUrl);

  } catch (error: any) {
    // Log detalhado de erro para debugging e segurança
    console.error('❌ Erro crítico na autenticação TikTok:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for')
    });

    // Resposta de erro segura (não expõe detalhes internos)
    return NextResponse.json({
      success: false,
      error: 'authentication_init_failed',
      message: 'Erro ao inicializar autenticação TikTok. Verifique as configurações.',
      troubleshooting: {
        check_env_vars: 'Verifique se TIKTOK_CLIENT_KEY, TIKTOK_CLIENT_SECRET e TIKTOK_REDIRECT_URI estão configurados',
        check_credentials: 'Confirme se as credenciais estão corretas no TikTok Developers',
        check_redirect_uri: 'Verifique se o redirect URI está autorizado no app TikTok'
      },
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
} 