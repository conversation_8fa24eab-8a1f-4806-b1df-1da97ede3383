[{"C:\\Users\\<USER>\\Documents\\aface-company\\app\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\providers.tsx": "4", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\sign-in\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\sign-up\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\action-buttons.tsx": "7", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\app-sidebar.tsx": "8", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\avatar.tsx": "9", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\badge.tsx": "10", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\breadcrumb.tsx": "11", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\button.tsx": "12", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\calendar.tsx": "13", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\card.tsx": "14", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-01.tsx": "15", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-02.tsx": "16", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-03.tsx": "17", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-04.tsx": "18", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-05.tsx": "19", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-06.tsx": "20", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart.tsx": "21", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\charts-extra.tsx": "22", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\collapsible.tsx": "23", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\date-picker.tsx": "24", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\dropdown-menu.tsx": "25", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\input.tsx": "26", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\label.tsx": "27", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\login\\LoginForm.tsx": "28", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\nav-user.tsx": "29", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\platform-sidebar.tsx": "30", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\popover.tsx": "31", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\radio-group.tsx": "32", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\separator.tsx": "33", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\sheet.tsx": "34", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\sidebar.tsx": "35", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\LanguageSelector.tsx": "36", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\LeftSideBranding.tsx": "37", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\PasswordRequirements.tsx": "38", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\ProgressBar.tsx": "39", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\SignupForm.tsx": "40", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\SocialSignupButtons.tsx": "41", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\skeleton.tsx": "42", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\tooltip.tsx": "43", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\accordion.tsx": "44", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\alert-dialog.tsx": "45", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\alert.tsx": "46", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\aspect-ratio.tsx": "47", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\avatar.tsx": "48", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\badge.tsx": "49", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\breadcrumb.tsx": "50", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\button.tsx": "51", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\calendar.tsx": "52", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\card.tsx": "53", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\carousel.tsx": "54", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\chart.tsx": "55", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\checkbox.tsx": "56", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\collapsible.tsx": "57", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\command.tsx": "58", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\context-menu.tsx": "59", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\dialog.tsx": "60", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\drawer.tsx": "61", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\dropdown-menu.tsx": "62", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\form.tsx": "63", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\hover-card.tsx": "64", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\input-otp.tsx": "65", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\input.tsx": "66", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\label.tsx": "67", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\menubar.tsx": "68", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\navigation-menu.tsx": "69", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\pagination.tsx": "70", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\popover.tsx": "71", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\progress.tsx": "72", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\radio-group.tsx": "73", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\resizable.tsx": "74", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\scroll-area.tsx": "75", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\select.tsx": "76", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\separator.tsx": "77", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sheet.tsx": "78", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sidebar.tsx": "79", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\skeleton.tsx": "80", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\slider.tsx": "81", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sonner.tsx": "82", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\switch.tsx": "83", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\table.tsx": "84", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\tabs.tsx": "85", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\textarea.tsx": "86", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toast.tsx": "87", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toaster.tsx": "88", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toggle-group.tsx": "89", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toggle.tsx": "90", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\tooltip.tsx": "91", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\use-toast.ts": "92", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\vertical-cut-reveal.tsx": "93", "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\auth.ts": "94", "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\supabase.ts": "95", "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\utils.ts": "96", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\auth\\callback\\route.ts": "97", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\auth\\login\\route.ts": "98", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\auth\\instagram\\login\\route.ts": "99", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\legal-terms\\page.tsx": "100", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\Footer.tsx": "101", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\Header.tsx": "102", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\instagram\\instagram-connection-status.tsx": "103", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\theme-wrapper.tsx": "104", "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\instagram-api.ts": "105", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\auth\\callback\\page.tsx": "106", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\privacy-policy\\page.tsx": "107", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\auth\\data-deletion\\route.ts": "108", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\auth\\deauthorize\\route.ts": "109", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\debug\\config\\route.ts": "110", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\diagnose\\route.ts": "111", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\insights\\route.ts": "112", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\test-insights\\route.ts": "113", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\tiktok\\auth\\callback\\route.ts": "114", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\tiktok\\auth\\login\\route.ts": "115", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\tiktok\\debug\\config\\route.ts": "116", "C:\\Users\\<USER>\\Documents\\aface-company\\app\\page.tsx": "117", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\instagram\\instagram-insights-dashboard.tsx": "118", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\instagram\\instagram-test-lab.tsx": "119", "C:\\Users\\<USER>\\Documents\\aface-company\\components\\tiktok\\tiktok-connection-status.tsx": "120", "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\tiktok-api.ts": "121"}, {"size": 1947, "mtime": 1752995906739, "results": "122", "hashOfConfig": "123"}, {"size": 5800, "mtime": 1753050026372, "results": "124", "hashOfConfig": "123"}, {"size": 772, "mtime": 1753000283619, "results": "125", "hashOfConfig": "123"}, {"size": 1127, "mtime": 1752985913218, "results": "126", "hashOfConfig": "123"}, {"size": 2109, "mtime": 1752997995180, "results": "127", "hashOfConfig": "123"}, {"size": 2270, "mtime": 1752997902790, "results": "128", "hashOfConfig": "123"}, {"size": 1684, "mtime": 1752908331412, "results": "129", "hashOfConfig": "123"}, {"size": 6816, "mtime": 1752961421879, "results": "130", "hashOfConfig": "123"}, {"size": 1124, "mtime": 1752908331605, "results": "131", "hashOfConfig": "123"}, {"size": 1512, "mtime": 1752908331609, "results": "132", "hashOfConfig": "123"}, {"size": 2402, "mtime": 1752908331619, "results": "133", "hashOfConfig": "123"}, {"size": 1981, "mtime": 1752908331623, "results": "134", "hashOfConfig": "123"}, {"size": 3922, "mtime": 1752908331637, "results": "135", "hashOfConfig": "123"}, {"size": 1535, "mtime": 1752908331641, "results": "136", "hashOfConfig": "123"}, {"size": 6833, "mtime": 1752908331464, "results": "137", "hashOfConfig": "123"}, {"size": 6228, "mtime": 1752908331492, "results": "138", "hashOfConfig": "123"}, {"size": 4890, "mtime": 1752908331514, "results": "139", "hashOfConfig": "123"}, {"size": 6201, "mtime": 1752908331551, "results": "140", "hashOfConfig": "123"}, {"size": 5590, "mtime": 1752908331560, "results": "141", "hashOfConfig": "123"}, {"size": 4421, "mtime": 1752908331566, "results": "142", "hashOfConfig": "123"}, {"size": 9853, "mtime": 1752908331675, "results": "143", "hashOfConfig": "123"}, {"size": 2489, "mtime": 1752908331570, "results": "144", "hashOfConfig": "123"}, {"size": 806, "mtime": 1752908331677, "results": "145", "hashOfConfig": "123"}, {"size": 6110, "mtime": 1752908331593, "results": "146", "hashOfConfig": "123"}, {"size": 9382, "mtime": 1752908331691, "results": "147", "hashOfConfig": "123"}, {"size": 955, "mtime": 1752908331694, "results": "148", "hashOfConfig": "123"}, {"size": 607, "mtime": 1752908331702, "results": "149", "hashOfConfig": "123"}, {"size": 9057, "mtime": 1752998027241, "results": "150", "hashOfConfig": "123"}, {"size": 4344, "mtime": 1752998248278, "results": "151", "hashOfConfig": "123"}, {"size": 18708, "mtime": 1753055281297, "results": "152", "hashOfConfig": "123"}, {"size": 1848, "mtime": 1752908331707, "results": "153", "hashOfConfig": "123"}, {"size": 1558, "mtime": 1752908331710, "results": "154", "hashOfConfig": "123"}, {"size": 711, "mtime": 1752908331715, "results": "155", "hashOfConfig": "123"}, {"size": 4109, "mtime": 1752908331736, "results": "156", "hashOfConfig": "123"}, {"size": 21919, "mtime": 1752910064331, "results": "157", "hashOfConfig": "123"}, {"size": 3244, "mtime": 1752997816065, "results": "158", "hashOfConfig": "123"}, {"size": 2671, "mtime": 1752904869127, "results": "159", "hashOfConfig": "123"}, {"size": 3492, "mtime": 1752892907000, "results": "160", "hashOfConfig": "123"}, {"size": 627, "mtime": 1752892907000, "results": "161", "hashOfConfig": "123"}, {"size": 14856, "mtime": 1752998111345, "results": "162", "hashOfConfig": "123"}, {"size": 2877, "mtime": 1752997573003, "results": "163", "hashOfConfig": "123"}, {"size": 283, "mtime": 1752908331842, "results": "164", "hashOfConfig": "123"}, {"size": 1912, "mtime": 1752908331849, "results": "165", "hashOfConfig": "123"}, {"size": 1977, "mtime": 1752892907000, "results": "166", "hashOfConfig": "123"}, {"size": 4420, "mtime": 1752892907000, "results": "167", "hashOfConfig": "123"}, {"size": 1584, "mtime": 1752892907000, "results": "168", "hashOfConfig": "123"}, {"size": 140, "mtime": 1752892907000, "results": "169", "hashOfConfig": "123"}, {"size": 1405, "mtime": 1752892907000, "results": "170", "hashOfConfig": "123"}, {"size": 1128, "mtime": 1752892907000, "results": "171", "hashOfConfig": "123"}, {"size": 2701, "mtime": 1752892907000, "results": "172", "hashOfConfig": "123"}, {"size": 1901, "mtime": 1752892907000, "results": "173", "hashOfConfig": "123"}, {"size": 2620, "mtime": 1752892907000, "results": "174", "hashOfConfig": "123"}, {"size": 1877, "mtime": 1752892907000, "results": "175", "hashOfConfig": "123"}, {"size": 6224, "mtime": 1752904805705, "results": "176", "hashOfConfig": "123"}, {"size": 10466, "mtime": 1752892907000, "results": "177", "hashOfConfig": "123"}, {"size": 1056, "mtime": 1752892907000, "results": "178", "hashOfConfig": "123"}, {"size": 315, "mtime": 1752892907000, "results": "179", "hashOfConfig": "123"}, {"size": 4910, "mtime": 1752961745697, "results": "180", "hashOfConfig": "123"}, {"size": 7246, "mtime": 1752892907000, "results": "181", "hashOfConfig": "123"}, {"size": 3835, "mtime": 1752910828461, "results": "182", "hashOfConfig": "123"}, {"size": 3007, "mtime": 1752892907000, "results": "183", "hashOfConfig": "123"}, {"size": 7295, "mtime": 1752892907000, "results": "184", "hashOfConfig": "123"}, {"size": 4085, "mtime": 1752892907000, "results": "185", "hashOfConfig": "123"}, {"size": 1184, "mtime": 1752892907000, "results": "186", "hashOfConfig": "123"}, {"size": 2154, "mtime": 1752892907000, "results": "187", "hashOfConfig": "123"}, {"size": 791, "mtime": 1752997594035, "results": "188", "hashOfConfig": "123"}, {"size": 710, "mtime": 1752892907000, "results": "189", "hashOfConfig": "123"}, {"size": 7974, "mtime": 1752892907000, "results": "190", "hashOfConfig": "123"}, {"size": 5046, "mtime": 1752892907000, "results": "191", "hashOfConfig": "123"}, {"size": 2751, "mtime": 1752892907000, "results": "192", "hashOfConfig": "123"}, {"size": 1230, "mtime": 1752892907000, "results": "193", "hashOfConfig": "123"}, {"size": 777, "mtime": 1752892907000, "results": "194", "hashOfConfig": "123"}, {"size": 1467, "mtime": 1752892907000, "results": "195", "hashOfConfig": "123"}, {"size": 1709, "mtime": 1752892907000, "results": "196", "hashOfConfig": "123"}, {"size": 1642, "mtime": 1752892907000, "results": "197", "hashOfConfig": "123"}, {"size": 5615, "mtime": 1752892907000, "results": "198", "hashOfConfig": "123"}, {"size": 756, "mtime": 1752892907000, "results": "199", "hashOfConfig": "123"}, {"size": 4250, "mtime": 1752892907000, "results": "200", "hashOfConfig": "123"}, {"size": 23383, "mtime": 1752910137715, "results": "201", "hashOfConfig": "123"}, {"size": 261, "mtime": 1752892907000, "results": "202", "hashOfConfig": "123"}, {"size": 1077, "mtime": 1752892907000, "results": "203", "hashOfConfig": "123"}, {"size": 909, "mtime": 1752985889191, "results": "204", "hashOfConfig": "123"}, {"size": 1139, "mtime": 1752892907000, "results": "205", "hashOfConfig": "123"}, {"size": 2765, "mtime": 1752892907000, "results": "206", "hashOfConfig": "123"}, {"size": 1883, "mtime": 1752892907000, "results": "207", "hashOfConfig": "123"}, {"size": 795, "mtime": 1752961757477, "results": "208", "hashOfConfig": "123"}, {"size": 4859, "mtime": 1752907793580, "results": "209", "hashOfConfig": "123"}, {"size": 786, "mtime": 1752907761076, "results": "210", "hashOfConfig": "123"}, {"size": 1739, "mtime": 1752892907000, "results": "211", "hashOfConfig": "123"}, {"size": 1435, "mtime": 1752892907000, "results": "212", "hashOfConfig": "123"}, {"size": 1145, "mtime": 1752892907000, "results": "213", "hashOfConfig": "123"}, {"size": 82, "mtime": 1752892907000, "results": "214", "hashOfConfig": "123"}, {"size": 6366, "mtime": 1752961887039, "results": "215", "hashOfConfig": "123"}, {"size": 8975, "mtime": 1752995583632, "results": "216", "hashOfConfig": "123"}, {"size": 903, "mtime": 1752995488280, "results": "217", "hashOfConfig": "123"}, {"size": 166, "mtime": 1752892907000, "results": "218", "hashOfConfig": "123"}, {"size": 4728, "mtime": 1753066442888, "results": "219", "hashOfConfig": "123"}, {"size": 1995, "mtime": 1753004293102, "results": "220", "hashOfConfig": "123"}, {"size": 1927, "mtime": 1753066442182, "results": "221", "hashOfConfig": "123"}, {"size": 138499, "mtime": 1753068341757, "results": "222", "hashOfConfig": "123"}, {"size": 8946, "mtime": 1753068220921, "results": "223", "hashOfConfig": "123"}, {"size": 5383, "mtime": 1753068241084, "results": "224", "hashOfConfig": "123"}, {"size": 20859, "mtime": 1753048502443, "results": "225", "hashOfConfig": "123"}, {"size": 1187, "mtime": 1752985880906, "results": "226", "hashOfConfig": "123"}, {"size": 23321, "mtime": 1753066442299, "results": "227", "hashOfConfig": "123"}, {"size": 3659, "mtime": 1753055281256, "results": "228", "hashOfConfig": "123"}, {"size": 24792, "mtime": 1753068367529, "results": "229", "hashOfConfig": "123"}, {"size": 7110, "mtime": 1753048502209, "results": "230", "hashOfConfig": "123"}, {"size": 6157, "mtime": 1753048502231, "results": "231", "hashOfConfig": "123"}, {"size": 2597, "mtime": 1753065747213, "results": "232", "hashOfConfig": "123"}, {"size": 5308, "mtime": 1753066441870, "results": "233", "hashOfConfig": "123"}, {"size": 7845, "mtime": 1753067775527, "results": "234", "hashOfConfig": "123"}, {"size": 9213, "mtime": 1753066441941, "results": "235", "hashOfConfig": "123"}, {"size": 8424, "mtime": 1753066442006, "results": "236", "hashOfConfig": "123"}, {"size": 3637, "mtime": 1753066442072, "results": "237", "hashOfConfig": "123"}, {"size": 3895, "mtime": 1753066442129, "results": "238", "hashOfConfig": "123"}, {"size": 168, "mtime": 1753000641420, "results": "239", "hashOfConfig": "123"}, {"size": 16193, "mtime": 1753068380492, "results": "240", "hashOfConfig": "123"}, {"size": 22646, "mtime": 1753049424926, "results": "241", "hashOfConfig": "123"}, {"size": 20266, "mtime": 1753055281255, "results": "242", "hashOfConfig": "123"}, {"size": 14636, "mtime": 1753055281230, "results": "243", "hashOfConfig": "123"}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cpsqhh", {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\aface-company\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\sign-in\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\sign-up\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\action-buttons.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\app-sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\calendar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-01.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-02.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-03.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-04.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-05.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart-06.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\chart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\charts-extra.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\date-picker.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\login\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\nav-user.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\platform-sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\separator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\LanguageSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\LeftSideBranding.tsx", ["607"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\PasswordRequirements.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\ProgressBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\SignupForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\signup\\SocialSignupButtons.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\calendar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\chart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\use-toast.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\vertical-cut-reveal.tsx", ["608", "609"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\auth\\callback\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\auth\\login\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\auth\\instagram\\login\\route.ts", ["610"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\legal-terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\Footer.tsx", ["611", "612", "613", "614", "615", "616", "617", "618", "619", "620"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\Header.tsx", ["621"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\instagram\\instagram-connection-status.tsx", ["622"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\theme-wrapper.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\instagram-api.ts", ["623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\auth\\callback\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\privacy-policy\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\auth\\data-deletion\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\auth\\deauthorize\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\debug\\config\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\diagnose\\route.ts", ["648", "649", "650", "651"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\insights\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\instagram\\test-insights\\route.ts", ["652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\tiktok\\auth\\callback\\route.ts", ["668"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\tiktok\\auth\\login\\route.ts", ["669"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\api\\tiktok\\debug\\config\\route.ts", ["670"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\instagram\\instagram-insights-dashboard.tsx", ["671", "672", "673", "674"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\instagram\\instagram-test-lab.tsx", ["675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689"], [], "C:\\Users\\<USER>\\Documents\\aface-company\\components\\tiktok\\tiktok-connection-status.tsx", [], [], "C:\\Users\\<USER>\\Documents\\aface-company\\lib\\tiktok-api.ts", [], [], {"ruleId": "690", "severity": 1, "message": "691", "line": 11, "column": 9, "nodeType": "692", "endLine": 17, "endColumn": 11}, {"ruleId": "693", "severity": 1, "message": "694", "line": 121, "column": 7, "nodeType": "695", "endLine": 121, "endColumn": 54, "suggestions": "696"}, {"ruleId": "693", "severity": 1, "message": "697", "line": 138, "column": 8, "nodeType": "695", "endLine": 138, "endColumn": 19, "suggestions": "698"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 44, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 44, "endColumn": 22, "suggestions": "703"}, {"ruleId": "690", "severity": 1, "message": "691", "line": 10, "column": 13, "nodeType": "692", "endLine": 14, "endColumn": 15}, {"ruleId": "690", "severity": 1, "message": "691", "line": 19, "column": 15, "nodeType": "692", "endLine": 23, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "691", "line": 26, "column": 15, "nodeType": "692", "endLine": 30, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "691", "line": 33, "column": 15, "nodeType": "692", "endLine": 37, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "691", "line": 40, "column": 15, "nodeType": "692", "endLine": 44, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "691", "line": 47, "column": 15, "nodeType": "692", "endLine": 51, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "691", "line": 54, "column": 15, "nodeType": "692", "endLine": 58, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "691", "line": 61, "column": 15, "nodeType": "692", "endLine": 65, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "691", "line": 122, "column": 17, "nodeType": "692", "endLine": 126, "endColumn": 19}, {"ruleId": "690", "severity": 1, "message": "691", "line": 129, "column": 17, "nodeType": "692", "endLine": 133, "endColumn": 19}, {"ruleId": "690", "severity": 1, "message": "691", "line": 18, "column": 13, "nodeType": "692", "endLine": 22, "endColumn": 15}, {"ruleId": "704", "severity": 1, "message": "705", "line": 195, "column": 23, "nodeType": "692", "endLine": 195, "endColumn": 52}, {"ruleId": "699", "severity": 1, "message": "700", "line": 81, "column": 53, "nodeType": "701", "messageId": "702", "endLine": 81, "endColumn": 56, "suggestions": "706"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 118, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 118, "endColumn": 24, "suggestions": "707"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 129, "column": 69, "nodeType": "701", "messageId": "702", "endLine": 129, "endColumn": 72, "suggestions": "708"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 153, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 153, "endColumn": 24, "suggestions": "709"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 164, "column": 54, "nodeType": "701", "messageId": "702", "endLine": 164, "endColumn": 57, "suggestions": "710"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 190, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 190, "endColumn": 24, "suggestions": "711"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 201, "column": 53, "nodeType": "701", "messageId": "702", "endLine": 201, "endColumn": 56, "suggestions": "712"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 225, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 225, "endColumn": 24, "suggestions": "713"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 255, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 255, "endColumn": 24, "suggestions": "714"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 273, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 273, "endColumn": 24, "suggestions": "715"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 312, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 312, "endColumn": 24, "suggestions": "716"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 346, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 346, "endColumn": 24, "suggestions": "717"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 391, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 391, "endColumn": 24, "suggestions": "718"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 418, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 418, "endColumn": 24, "suggestions": "719"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 452, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 452, "endColumn": 24, "suggestions": "720"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 461, "column": 41, "nodeType": "701", "messageId": "702", "endLine": 461, "endColumn": 44, "suggestions": "721"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 466, "column": 44, "nodeType": "701", "messageId": "702", "endLine": 466, "endColumn": 47, "suggestions": "722"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 477, "column": 42, "nodeType": "701", "messageId": "702", "endLine": 477, "endColumn": 45, "suggestions": "723"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 477, "column": 55, "nodeType": "701", "messageId": "702", "endLine": 477, "endColumn": 58, "suggestions": "724"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 530, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 530, "endColumn": 24, "suggestions": "725"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 553, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 553, "endColumn": 24, "suggestions": "726"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 616, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 616, "endColumn": 24, "suggestions": "727"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 625, "column": 42, "nodeType": "701", "messageId": "702", "endLine": 625, "endColumn": 45, "suggestions": "728"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 626, "column": 23, "nodeType": "701", "messageId": "702", "endLine": 626, "endColumn": 26, "suggestions": "729"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 706, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 706, "endColumn": 24, "suggestions": "730"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 31, "column": 29, "nodeType": "701", "messageId": "702", "endLine": 31, "endColumn": 32, "suggestions": "731"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 32, "column": 20, "nodeType": "701", "messageId": "702", "endLine": 32, "endColumn": 23, "suggestions": "732"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 33, "column": 22, "nodeType": "701", "messageId": "702", "endLine": 33, "endColumn": 25, "suggestions": "733"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 139, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 139, "endColumn": 22, "suggestions": "734"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 29, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 29, "endColumn": 24, "suggestions": "735"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 30, "column": 22, "nodeType": "701", "messageId": "702", "endLine": 30, "endColumn": 25, "suggestions": "736"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 31, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 31, "endColumn": 22, "suggestions": "737"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 32, "column": 23, "nodeType": "701", "messageId": "702", "endLine": 32, "endColumn": 26, "suggestions": "738"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 33, "column": 26, "nodeType": "701", "messageId": "702", "endLine": 33, "endColumn": 29, "suggestions": "739"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 34, "column": 15, "nodeType": "701", "messageId": "702", "endLine": 34, "endColumn": 18, "suggestions": "740"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 52, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 52, "endColumn": 24, "suggestions": "741"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 68, "column": 25, "nodeType": "701", "messageId": "702", "endLine": 68, "endColumn": 28, "suggestions": "742"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 88, "column": 78, "nodeType": "701", "messageId": "702", "endLine": 88, "endColumn": 81, "suggestions": "743"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 90, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 90, "endColumn": 24, "suggestions": "744"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 113, "column": 25, "nodeType": "701", "messageId": "702", "endLine": 113, "endColumn": 28, "suggestions": "745"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 150, "column": 25, "nodeType": "701", "messageId": "702", "endLine": 150, "endColumn": 28, "suggestions": "746"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 159, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 159, "endColumn": 24, "suggestions": "747"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 195, "column": 27, "nodeType": "701", "messageId": "702", "endLine": 195, "endColumn": 30, "suggestions": "748"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 206, "column": 23, "nodeType": "701", "messageId": "702", "endLine": 206, "endColumn": 26, "suggestions": "749"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 230, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 230, "endColumn": 22, "suggestions": "750"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 179, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 179, "endColumn": 22, "suggestions": "751"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 69, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 69, "endColumn": 22, "suggestions": "752"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 76, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 76, "endColumn": 22, "suggestions": "753"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 25, "column": 15, "nodeType": "701", "messageId": "702", "endLine": 25, "endColumn": 18, "suggestions": "754"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 109, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 109, "endColumn": 24, "suggestions": "755"}, {"ruleId": "693", "severity": 1, "message": "756", "line": 127, "column": 6, "nodeType": "695", "endLine": 127, "endColumn": 29, "suggestions": "757"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 379, "column": 89, "nodeType": "701", "messageId": "702", "endLine": 379, "endColumn": 92, "suggestions": "758"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 18, "column": 17, "nodeType": "701", "messageId": "702", "endLine": 18, "endColumn": 20, "suggestions": "759"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 19, "column": 18, "nodeType": "701", "messageId": "702", "endLine": 19, "endColumn": 21, "suggestions": "760"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 20, "column": 15, "nodeType": "701", "messageId": "702", "endLine": 20, "endColumn": 18, "suggestions": "761"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 21, "column": 19, "nodeType": "701", "messageId": "702", "endLine": 21, "endColumn": 22, "suggestions": "762"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 22, "column": 22, "nodeType": "701", "messageId": "702", "endLine": 22, "endColumn": 25, "suggestions": "763"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 23, "column": 11, "nodeType": "701", "messageId": "702", "endLine": 23, "endColumn": 14, "suggestions": "764"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 90, "column": 21, "nodeType": "701", "messageId": "702", "endLine": 90, "endColumn": 24, "suggestions": "765"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 104, "column": 31, "nodeType": "701", "messageId": "702", "endLine": 104, "endColumn": 34, "suggestions": "766"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 207, "column": 17, "nodeType": "692", "endLine": 207, "endColumn": 62}, {"ruleId": "699", "severity": 1, "message": "700", "line": 370, "column": 96, "nodeType": "701", "messageId": "702", "endLine": 370, "endColumn": 99, "suggestions": "767"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 381, "column": 92, "nodeType": "701", "messageId": "702", "endLine": 381, "endColumn": 95, "suggestions": "768"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 412, "column": 62, "nodeType": "701", "messageId": "702", "endLine": 412, "endColumn": 65, "suggestions": "769"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 462, "column": 101, "nodeType": "701", "messageId": "702", "endLine": 462, "endColumn": 104, "suggestions": "770"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 485, "column": 99, "nodeType": "701", "messageId": "702", "endLine": 485, "endColumn": 102, "suggestions": "771"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 521, "column": 53, "nodeType": "701", "messageId": "702", "endLine": 521, "endColumn": 56, "suggestions": "772"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'elements' and 'splitBy'. Either include them or remove the dependency array.", "ArrayExpression", ["773"], "React Hook useEffect has a missing dependency: 'startAnimation'. Either include it or remove the dependency array.", ["774"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["775", "776"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["777", "778"], ["779", "780"], ["781", "782"], ["783", "784"], ["785", "786"], ["787", "788"], ["789", "790"], ["791", "792"], ["793", "794"], ["795", "796"], ["797", "798"], ["799", "800"], ["801", "802"], ["803", "804"], ["805", "806"], ["807", "808"], ["809", "810"], ["811", "812"], ["813", "814"], ["815", "816"], ["817", "818"], ["819", "820"], ["821", "822"], ["823", "824"], ["825", "826"], ["827", "828"], ["829", "830"], ["831", "832"], ["833", "834"], ["835", "836"], ["837", "838"], ["839", "840"], ["841", "842"], ["843", "844"], ["845", "846"], ["847", "848"], ["849", "850"], ["851", "852"], ["853", "854"], ["855", "856"], ["857", "858"], ["859", "860"], ["861", "862"], ["863", "864"], ["865", "866"], ["867", "868"], ["869", "870"], ["871", "872"], ["873", "874"], ["875", "876"], "React Hook useEffect has a missing dependency: 'loadInsights'. Either include it or remove the dependency array.", ["877"], ["878", "879"], ["880", "881"], ["882", "883"], ["884", "885"], ["886", "887"], ["888", "889"], ["890", "891"], ["892", "893"], ["894", "895"], ["896", "897"], ["898", "899"], ["900", "901"], ["902", "903"], ["904", "905"], ["906", "907"], {"desc": "908", "fix": "909"}, {"desc": "910", "fix": "911"}, {"messageId": "912", "fix": "913", "desc": "914"}, {"messageId": "915", "fix": "916", "desc": "917"}, {"messageId": "912", "fix": "918", "desc": "914"}, {"messageId": "915", "fix": "919", "desc": "917"}, {"messageId": "912", "fix": "920", "desc": "914"}, {"messageId": "915", "fix": "921", "desc": "917"}, {"messageId": "912", "fix": "922", "desc": "914"}, {"messageId": "915", "fix": "923", "desc": "917"}, {"messageId": "912", "fix": "924", "desc": "914"}, {"messageId": "915", "fix": "925", "desc": "917"}, {"messageId": "912", "fix": "926", "desc": "914"}, {"messageId": "915", "fix": "927", "desc": "917"}, {"messageId": "912", "fix": "928", "desc": "914"}, {"messageId": "915", "fix": "929", "desc": "917"}, {"messageId": "912", "fix": "930", "desc": "914"}, {"messageId": "915", "fix": "931", "desc": "917"}, {"messageId": "912", "fix": "932", "desc": "914"}, {"messageId": "915", "fix": "933", "desc": "917"}, {"messageId": "912", "fix": "934", "desc": "914"}, {"messageId": "915", "fix": "935", "desc": "917"}, {"messageId": "912", "fix": "936", "desc": "914"}, {"messageId": "915", "fix": "937", "desc": "917"}, {"messageId": "912", "fix": "938", "desc": "914"}, {"messageId": "915", "fix": "939", "desc": "917"}, {"messageId": "912", "fix": "940", "desc": "914"}, {"messageId": "915", "fix": "941", "desc": "917"}, {"messageId": "912", "fix": "942", "desc": "914"}, {"messageId": "915", "fix": "943", "desc": "917"}, {"messageId": "912", "fix": "944", "desc": "914"}, {"messageId": "915", "fix": "945", "desc": "917"}, {"messageId": "912", "fix": "946", "desc": "914"}, {"messageId": "915", "fix": "947", "desc": "917"}, {"messageId": "912", "fix": "948", "desc": "914"}, {"messageId": "915", "fix": "949", "desc": "917"}, {"messageId": "912", "fix": "950", "desc": "914"}, {"messageId": "915", "fix": "951", "desc": "917"}, {"messageId": "912", "fix": "952", "desc": "914"}, {"messageId": "915", "fix": "953", "desc": "917"}, {"messageId": "912", "fix": "954", "desc": "914"}, {"messageId": "915", "fix": "955", "desc": "917"}, {"messageId": "912", "fix": "956", "desc": "914"}, {"messageId": "915", "fix": "957", "desc": "917"}, {"messageId": "912", "fix": "958", "desc": "914"}, {"messageId": "915", "fix": "959", "desc": "917"}, {"messageId": "912", "fix": "960", "desc": "914"}, {"messageId": "915", "fix": "961", "desc": "917"}, {"messageId": "912", "fix": "962", "desc": "914"}, {"messageId": "915", "fix": "963", "desc": "917"}, {"messageId": "912", "fix": "964", "desc": "914"}, {"messageId": "915", "fix": "965", "desc": "917"}, {"messageId": "912", "fix": "966", "desc": "914"}, {"messageId": "915", "fix": "967", "desc": "917"}, {"messageId": "912", "fix": "968", "desc": "914"}, {"messageId": "915", "fix": "969", "desc": "917"}, {"messageId": "912", "fix": "970", "desc": "914"}, {"messageId": "915", "fix": "971", "desc": "917"}, {"messageId": "912", "fix": "972", "desc": "914"}, {"messageId": "915", "fix": "973", "desc": "917"}, {"messageId": "912", "fix": "974", "desc": "914"}, {"messageId": "915", "fix": "975", "desc": "917"}, {"messageId": "912", "fix": "976", "desc": "914"}, {"messageId": "915", "fix": "977", "desc": "917"}, {"messageId": "912", "fix": "978", "desc": "914"}, {"messageId": "915", "fix": "979", "desc": "917"}, {"messageId": "912", "fix": "980", "desc": "914"}, {"messageId": "915", "fix": "981", "desc": "917"}, {"messageId": "912", "fix": "982", "desc": "914"}, {"messageId": "915", "fix": "983", "desc": "917"}, {"messageId": "912", "fix": "984", "desc": "914"}, {"messageId": "915", "fix": "985", "desc": "917"}, {"messageId": "912", "fix": "986", "desc": "914"}, {"messageId": "915", "fix": "987", "desc": "917"}, {"messageId": "912", "fix": "988", "desc": "914"}, {"messageId": "915", "fix": "989", "desc": "917"}, {"messageId": "912", "fix": "990", "desc": "914"}, {"messageId": "915", "fix": "991", "desc": "917"}, {"messageId": "912", "fix": "992", "desc": "914"}, {"messageId": "915", "fix": "993", "desc": "917"}, {"messageId": "912", "fix": "994", "desc": "914"}, {"messageId": "915", "fix": "995", "desc": "917"}, {"messageId": "912", "fix": "996", "desc": "914"}, {"messageId": "915", "fix": "997", "desc": "917"}, {"messageId": "912", "fix": "998", "desc": "914"}, {"messageId": "915", "fix": "999", "desc": "917"}, {"messageId": "912", "fix": "1000", "desc": "914"}, {"messageId": "915", "fix": "1001", "desc": "917"}, {"messageId": "912", "fix": "1002", "desc": "914"}, {"messageId": "915", "fix": "1003", "desc": "917"}, {"messageId": "912", "fix": "1004", "desc": "914"}, {"messageId": "915", "fix": "1005", "desc": "917"}, {"messageId": "912", "fix": "1006", "desc": "914"}, {"messageId": "915", "fix": "1007", "desc": "917"}, {"messageId": "912", "fix": "1008", "desc": "914"}, {"messageId": "915", "fix": "1009", "desc": "917"}, {"messageId": "912", "fix": "1010", "desc": "914"}, {"messageId": "915", "fix": "1011", "desc": "917"}, {"messageId": "912", "fix": "1012", "desc": "914"}, {"messageId": "915", "fix": "1013", "desc": "917"}, {"messageId": "912", "fix": "1014", "desc": "914"}, {"messageId": "915", "fix": "1015", "desc": "917"}, {"messageId": "912", "fix": "1016", "desc": "914"}, {"messageId": "915", "fix": "1017", "desc": "917"}, {"desc": "1018", "fix": "1019"}, {"messageId": "912", "fix": "1020", "desc": "914"}, {"messageId": "915", "fix": "1021", "desc": "917"}, {"messageId": "912", "fix": "1022", "desc": "914"}, {"messageId": "915", "fix": "1023", "desc": "917"}, {"messageId": "912", "fix": "1024", "desc": "914"}, {"messageId": "915", "fix": "1025", "desc": "917"}, {"messageId": "912", "fix": "1026", "desc": "914"}, {"messageId": "915", "fix": "1027", "desc": "917"}, {"messageId": "912", "fix": "1028", "desc": "914"}, {"messageId": "915", "fix": "1029", "desc": "917"}, {"messageId": "912", "fix": "1030", "desc": "914"}, {"messageId": "915", "fix": "1031", "desc": "917"}, {"messageId": "912", "fix": "1032", "desc": "914"}, {"messageId": "915", "fix": "1033", "desc": "917"}, {"messageId": "912", "fix": "1034", "desc": "914"}, {"messageId": "915", "fix": "1035", "desc": "917"}, {"messageId": "912", "fix": "1036", "desc": "914"}, {"messageId": "915", "fix": "1037", "desc": "917"}, {"messageId": "912", "fix": "1038", "desc": "914"}, {"messageId": "915", "fix": "1039", "desc": "917"}, {"messageId": "912", "fix": "1040", "desc": "914"}, {"messageId": "915", "fix": "1041", "desc": "917"}, {"messageId": "912", "fix": "1042", "desc": "914"}, {"messageId": "915", "fix": "1043", "desc": "917"}, {"messageId": "912", "fix": "1044", "desc": "914"}, {"messageId": "915", "fix": "1045", "desc": "917"}, {"messageId": "912", "fix": "1046", "desc": "914"}, {"messageId": "915", "fix": "1047", "desc": "917"}, {"messageId": "912", "fix": "1048", "desc": "914"}, {"messageId": "915", "fix": "1049", "desc": "917"}, "Update the dependencies array to be: [splitBy, elements, staggerFrom, staggerDuration]", {"range": "1050", "text": "1051"}, "Update the dependencies array to be: [autoStart, startAnimation]", {"range": "1052", "text": "1053"}, "suggestUnknown", {"range": "1054", "text": "1055"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1056", "text": "1057"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1058", "text": "1055"}, {"range": "1059", "text": "1057"}, {"range": "1060", "text": "1055"}, {"range": "1061", "text": "1057"}, {"range": "1062", "text": "1055"}, {"range": "1063", "text": "1057"}, {"range": "1064", "text": "1055"}, {"range": "1065", "text": "1057"}, {"range": "1066", "text": "1055"}, {"range": "1067", "text": "1057"}, {"range": "1068", "text": "1055"}, {"range": "1069", "text": "1057"}, {"range": "1070", "text": "1055"}, {"range": "1071", "text": "1057"}, {"range": "1072", "text": "1055"}, {"range": "1073", "text": "1057"}, {"range": "1074", "text": "1055"}, {"range": "1075", "text": "1057"}, {"range": "1076", "text": "1055"}, {"range": "1077", "text": "1057"}, {"range": "1078", "text": "1055"}, {"range": "1079", "text": "1057"}, {"range": "1080", "text": "1055"}, {"range": "1081", "text": "1057"}, {"range": "1082", "text": "1055"}, {"range": "1083", "text": "1057"}, {"range": "1084", "text": "1055"}, {"range": "1085", "text": "1057"}, {"range": "1086", "text": "1055"}, {"range": "1087", "text": "1057"}, {"range": "1088", "text": "1055"}, {"range": "1089", "text": "1057"}, {"range": "1090", "text": "1055"}, {"range": "1091", "text": "1057"}, {"range": "1092", "text": "1055"}, {"range": "1093", "text": "1057"}, {"range": "1094", "text": "1055"}, {"range": "1095", "text": "1057"}, {"range": "1096", "text": "1055"}, {"range": "1097", "text": "1057"}, {"range": "1098", "text": "1055"}, {"range": "1099", "text": "1057"}, {"range": "1100", "text": "1055"}, {"range": "1101", "text": "1057"}, {"range": "1102", "text": "1055"}, {"range": "1103", "text": "1057"}, {"range": "1104", "text": "1055"}, {"range": "1105", "text": "1057"}, {"range": "1106", "text": "1055"}, {"range": "1107", "text": "1057"}, {"range": "1108", "text": "1055"}, {"range": "1109", "text": "1057"}, {"range": "1110", "text": "1055"}, {"range": "1111", "text": "1057"}, {"range": "1112", "text": "1055"}, {"range": "1113", "text": "1057"}, {"range": "1114", "text": "1055"}, {"range": "1115", "text": "1057"}, {"range": "1116", "text": "1055"}, {"range": "1117", "text": "1057"}, {"range": "1118", "text": "1055"}, {"range": "1119", "text": "1057"}, {"range": "1120", "text": "1055"}, {"range": "1121", "text": "1057"}, {"range": "1122", "text": "1055"}, {"range": "1123", "text": "1057"}, {"range": "1124", "text": "1055"}, {"range": "1125", "text": "1057"}, {"range": "1126", "text": "1055"}, {"range": "1127", "text": "1057"}, {"range": "1128", "text": "1055"}, {"range": "1129", "text": "1057"}, {"range": "1130", "text": "1055"}, {"range": "1131", "text": "1057"}, {"range": "1132", "text": "1055"}, {"range": "1133", "text": "1057"}, {"range": "1134", "text": "1055"}, {"range": "1135", "text": "1057"}, {"range": "1136", "text": "1055"}, {"range": "1137", "text": "1057"}, {"range": "1138", "text": "1055"}, {"range": "1139", "text": "1057"}, {"range": "1140", "text": "1055"}, {"range": "1141", "text": "1057"}, {"range": "1142", "text": "1055"}, {"range": "1143", "text": "1057"}, {"range": "1144", "text": "1055"}, {"range": "1145", "text": "1057"}, {"range": "1146", "text": "1055"}, {"range": "1147", "text": "1057"}, {"range": "1148", "text": "1055"}, {"range": "1149", "text": "1057"}, {"range": "1150", "text": "1055"}, {"range": "1151", "text": "1057"}, {"range": "1152", "text": "1055"}, {"range": "1153", "text": "1057"}, {"range": "1154", "text": "1055"}, {"range": "1155", "text": "1057"}, {"range": "1156", "text": "1055"}, {"range": "1157", "text": "1057"}, "Update the dependencies array to be: [isAuthenticated, loadInsights, user]", {"range": "1158", "text": "1159"}, {"range": "1160", "text": "1055"}, {"range": "1161", "text": "1057"}, {"range": "1162", "text": "1055"}, {"range": "1163", "text": "1057"}, {"range": "1164", "text": "1055"}, {"range": "1165", "text": "1057"}, {"range": "1166", "text": "1055"}, {"range": "1167", "text": "1057"}, {"range": "1168", "text": "1055"}, {"range": "1169", "text": "1057"}, {"range": "1170", "text": "1055"}, {"range": "1171", "text": "1057"}, {"range": "1172", "text": "1055"}, {"range": "1173", "text": "1057"}, {"range": "1174", "text": "1055"}, {"range": "1175", "text": "1057"}, {"range": "1176", "text": "1055"}, {"range": "1177", "text": "1057"}, {"range": "1178", "text": "1055"}, {"range": "1179", "text": "1057"}, {"range": "1180", "text": "1055"}, {"range": "1181", "text": "1057"}, {"range": "1182", "text": "1055"}, {"range": "1183", "text": "1057"}, {"range": "1184", "text": "1055"}, {"range": "1185", "text": "1057"}, {"range": "1186", "text": "1055"}, {"range": "1187", "text": "1057"}, {"range": "1188", "text": "1055"}, {"range": "1189", "text": "1057"}, [3546, 3593], "[splitBy, elements, staggerFrom, staggerDuration]", [3908, 3919], "[autoStart, startAnimation]", [1671, 1674], "unknown", [1671, 1674], "never", [2931, 2934], [2931, 2934], [4274, 4277], [4274, 4277], [4751, 4754], [4751, 4754], [5446, 5449], [5446, 5449], [5847, 5850], [5847, 5850], [6693, 6696], [6693, 6696], [7104, 7107], [7104, 7107], [7771, 7774], [7771, 7774], [8804, 8807], [8804, 8807], [9286, 9289], [9286, 9289], [10582, 10585], [10582, 10585], [11627, 11630], [11627, 11630], [13255, 13258], [13255, 13258], [14036, 14039], [14036, 14039], [15019, 15022], [15019, 15022], [15264, 15267], [15264, 15267], [15395, 15398], [15395, 15398], [15774, 15777], [15774, 15777], [15787, 15790], [15787, 15790], [17304, 17307], [17304, 17307], [17879, 17882], [17879, 17882], [19814, 19817], [19814, 19817], [20053, 20056], [20053, 20056], [20084, 20087], [20084, 20087], [22787, 22790], [22787, 22790], [952, 955], [952, 955], [976, 979], [976, 979], [1004, 1007], [1004, 1007], [4977, 4980], [4977, 4980], [829, 832], [829, 832], [855, 858], [855, 858], [878, 881], [878, 881], [905, 908], [905, 908], [935, 938], [935, 938], [954, 957], [954, 957], [1528, 1531], [1528, 1531], [2318, 2321], [2318, 2321], [3192, 3195], [3192, 3195], [3244, 3247], [3244, 3247], [4310, 4313], [4310, 4313], [5661, 5664], [5661, 5664], [5937, 5940], [5937, 5940], [7374, 7377], [7374, 7377], [7817, 7820], [7817, 7820], [8835, 8838], [8835, 8838], [7240, 7243], [7240, 7243], [2569, 2572], [2569, 2572], [3098, 3101], [3098, 3101], [975, 978], [975, 978], [3567, 3570], [3567, 3570], [3969, 3992], "[isAuthenticated, loadInsights, user]", [14272, 14275], [14272, 14275], [711, 714], [711, 714], [734, 737], [734, 737], [754, 757], [754, 757], [778, 781], [778, 781], [805, 808], [805, 808], [821, 824], [821, 824], [2786, 2789], [2786, 2789], [3114, 3117], [3114, 3117], [13550, 13553], [13550, 13553], [14250, 14253], [14250, 14253], [15635, 15638], [15635, 15638], [17915, 17918], [17915, 17918], [19298, 19301], [19298, 19301], [20831, 20834], [20831, 20834]]