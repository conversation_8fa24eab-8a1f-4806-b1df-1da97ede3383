"use client";

import { useInstagramAuth } from '@/hooks/use-instagram-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { CheckCircle, XCircle, Instagram, User, Users, Image, Loader2, Shield, Trash2, Settings, ExternalLink, Copy, CheckCheck } from 'lucide-react';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';

export function InstagramConnectionStatus() {
  const { isAuthenticated, user, error, authStatus, logout, clearError, isLoading } = useInstagramAuth();
  const { toast } = useToast();
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [confirmationCode, setConfirmationCode] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
      toast({
        title: "Copiado!",
        description: "Código copiado para a área de transferência",
      });
    } catch (err) {
      console.error('Erro ao copiar:', err);
    }
  };

  const handleDisconnect = async () => {
    try {
      setIsProcessing(true);
      
      // Limpa dados locais
      logout();
      
      // Em produção, você também faria:
      // - Revogação do token no Instagram
      // - Limpeza de dados no banco
      
      setShowDisconnectDialog(false);
      console.log('✅ Conta desconectada com sucesso');
      
      toast({
        title: "Desconectado com sucesso!",
        description: "Sua conta do Instagram foi desconectada. Você pode se reconectar a qualquer momento.",
      });
      
    } catch (error) {
      console.error('❌ Erro ao desconectar:', error);
      toast({
        variant: "destructive",
        title: "Erro ao desconectar",
        description: "Não foi possível desconectar sua conta. Tente novamente.",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteData = async () => {
    try {
      setIsProcessing(true);
      
      if (!user?.id) {
        throw new Error('ID do usuário não encontrado');
      }

      // Chama o endpoint de exclusão de dados
      const response = await fetch(`/api/auth/data-deletion?user_id=${user.id}`);
      const data = await response.json();
      
      if (data.success) {
        // Mostra popup de sucesso em vez de redirecionar
        setConfirmationCode(data.confirmation_code);
        setShowDeleteDialog(false);
        setShowSuccessDialog(true);
        
        // Limpa dados locais após exclusão
        logout();
        
      } else {
        throw new Error(data.message || 'Erro ao solicitar exclusão de dados');
      }
      
    } catch (error) {
      console.error('❌ Erro ao excluir dados:', error);
      toast({
        variant: "destructive",
        title: "Erro na exclusão",
        description: "Erro ao processar solicitação de exclusão. Tente novamente.",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400 rounded-lg flex items-center justify-center">
              <Instagram className="w-5 h-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">Conexão Instagram</CardTitle>
              <CardDescription>
                Status da sua autenticação com o Instagram Business
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Status da Conexão */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-400">
                    Verificando...
                  </span>
                </>
              ) : isAuthenticated ? (
                <>
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-green-700 dark:text-green-400">
                    Conectado
                  </span>
                </>
              ) : (
                <>
                  <XCircle className="w-5 h-5 text-red-500" />
                  <span className="text-sm font-medium text-red-700 dark:text-red-400">
                    Desconectado
                  </span>
                </>
              )}
            </div>
            
            <Badge variant={isAuthenticated ? "default" : isLoading ? "outline" : "secondary"}>
              {isLoading ? 'Verificando...' :
               authStatus === 'success' ? 'Ativo' : 
               authStatus === 'error' ? 'Erro' : 
               authStatus === 'pending' ? 'Conectando...' : 'Inativo'}
            </Badge>
          </div>

          {/* Informações do Usuário */}
          {isAuthenticated && user && (
            <div className="mt-4 p-3 bg-muted/50 rounded-lg">
              <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                <User className="w-4 h-4" />
                Perfil Conectado
              </h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center justify-between">
                  <span>Usuário:</span>
                  <span className="font-medium">@{user.username}</span>
                </div>
                {user.name && (
                  <div className="flex items-center justify-between">
                    <span>Nome:</span>
                    <span className="font-medium">{user.name}</span>
                  </div>
                )}
                {user.account_type && (
                  <div className="flex items-center justify-between">
                    <span>Tipo:</span>
                    <span className="font-medium">{user.account_type}</span>
                  </div>
                )}
                {user.followers_count && (
                  <div className="flex items-center justify-between">
                    <span>Seguidores:</span>
                    <span className="font-medium flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {user.followers_count.toLocaleString()}
                    </span>
                  </div>
                )}
                {user.media_count && (
                  <div className="flex items-center justify-between">
                    <span>Posts:</span>
                    <span className="font-medium flex items-center gap-1">
                      <Image className="w-3 h-3" />
                      {user.media_count.toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Ações */}
          <div className="mt-4 flex flex-wrap gap-2">
            {isAuthenticated ? (
              <>
                {/* Botão Desconectar */}
                <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-orange-600 hover:text-orange-700 dark:text-orange-400"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Desconectar
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <Settings className="w-5 h-5 text-orange-500" />
                        Desconectar Instagram
                      </DialogTitle>
                      <DialogDescription>
                        Você será desconectado do Instagram, mas seus dados permanecerão salvos. 
                        Você pode se reconectar a qualquer momento.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-3">
                      <Alert>
                        <Shield className="w-4 h-4" />
                        <AlertDescription>
                          <strong>O que acontece ao desconectar:</strong>
                          <ul className="mt-2 space-y-1 text-sm">
                            <li>• Sua sessão local será encerrada</li>
                            <li>• Os tokens de acesso serão removidos</li>
                            <li>• Dados históricos permanecem salvos</li>
                            <li>• Você pode se reconectar quando quiser</li>
                          </ul>
                        </AlertDescription>
                      </Alert>
                    </div>
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setShowDisconnectDialog(false)}
                        disabled={isProcessing}
                      >
                        Cancelar
                      </Button>
                      <Button 
                        onClick={handleDisconnect}
                        disabled={isProcessing}
                        className="bg-orange-500 hover:bg-orange-600"
                      >
                        {isProcessing ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Settings className="w-4 h-4 mr-2" />
                        )}
                        Desconectar
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                {/* Botão Excluir Dados */}
                <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700 dark:text-red-400 border-red-200 hover:border-red-300 dark:border-red-800"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Excluir Dados
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
                        <Trash2 className="w-5 h-5" />
                        Excluir Todos os Dados
                      </DialogTitle>
                      <DialogDescription>
                        Esta ação irá excluir permanentemente todos os seus dados do Instagram de nossos sistemas.
                        <strong className="text-red-600 dark:text-red-400"> Esta ação não pode ser desfeita.</strong>
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-3">
                      <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
                        <Trash2 className="w-4 h-4 text-red-500" />
                        <AlertDescription className="text-red-700 dark:text-red-300">
                          <strong>Dados que serão excluídos permanentemente:</strong>
                          <ul className="mt-2 space-y-1 text-sm">
                            <li>• Informações do perfil (@{user?.username})</li>
                            <li>• Tokens de acesso e autorização</li>
                            <li>• Histórico de analytics e insights</li>
                            <li>• Cache de mídia e metadados</li>
                            <li>• Todas as configurações e preferências</li>
                          </ul>
                        </AlertDescription>
                      </Alert>
                      <Alert>
                        <Shield className="w-4 h-4" />
                        <AlertDescription>
                          <strong>Conformidade com LGPD/GDPR:</strong><br />
                          Este processo está em conformidade com as leis de proteção de dados. 
                          Você receberá um código de confirmação para acompanhar o status da exclusão.
                        </AlertDescription>
                      </Alert>
                    </div>
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setShowDeleteDialog(false)}
                        disabled={isProcessing}
                      >
                        Cancelar
                      </Button>
                      <Button 
                        variant="destructive"
                        onClick={handleDeleteData}
                        disabled={isProcessing}
                      >
                        {isProcessing ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Trash2 className="w-4 h-4 mr-2" />
                        )}
                        Excluir Permanentemente
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </>
            ) : (
              <Button
                size="sm"
                onClick={async () => {
                  clearError();
                  try {
                    // Chama a API para obter a URL de autorização
                    const response = await fetch('/auth/instagram/login');
                    const data = await response.json();
                    
                    if (data.success && data.authUrl) {
                      // Redireciona para a URL de autorização do Instagram
                      window.location.href = data.authUrl;
                    } else {
                      console.error('Erro ao obter URL de autorização:', data);
                    }
                  } catch (error) {
                    console.error('Erro ao conectar com Instagram:', error);
                  }
                }}
                className="bg-gradient-to-r from-purple-500 via-pink-500 to-orange-400 hover:from-purple-600 hover:via-pink-600 hover:to-orange-500 text-white"
              >
                <Instagram className="w-4 h-4 mr-2" />
                Conectar Instagram
              </Button>
            )}
          </div>

          {/* Link para Política de Privacidade */}
          {isAuthenticated && (
            <div className="pt-3 border-t">
              <p className="text-xs text-muted-foreground">
                Dúvidas sobre seus dados? Consulte nossa{' '}
                <a 
                  href="/privacy-policy" 
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 inline-flex items-center gap-1"
                >
                  Política de Privacidade
                  <ExternalLink className="w-3 h-3" />
                </a>
              </p>
            </div>
          )}

          {/* Mensagem de Erro */}
          {error && (
            <Alert className="mt-4 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
              <XCircle className="w-4 h-4 text-red-500" />
              <AlertDescription className="text-red-700 dark:text-red-300">
                <div className="space-y-2">
                  <p>{error}</p>
                  {error.includes('redirect_uri') && (
                    <div className="text-xs">
                      <p>Configure no Meta Developer Console:</p>
                      <code className="bg-red-100 dark:bg-red-900 px-1 rounded">http://localhost:3000/api/instagram/auth/callback</code>
                    </div>
                  )}
                  <Button
                    variant="link"
                    size="sm"
                    onClick={clearError}
                    className="ml-0 p-0 h-auto text-red-600 hover:text-red-700"
                  >
                    Dispensar
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Dialog de Sucesso na Exclusão */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-4">
              <CheckCheck className="w-6 h-6 text-green-600 dark:text-green-400 animate-pulse" />
            </div>
            <DialogTitle className="text-center text-green-700 dark:text-green-300">
              Dados Excluídos com Sucesso! ✨
            </DialogTitle>
            <DialogDescription className="text-center">
              Todos os seus dados do Instagram foram permanentemente removidos de nossos sistemas.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Código de confirmação */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Código de Confirmação
              </label>
              <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg">
                <code className="flex-1 text-sm font-mono text-green-800 dark:text-green-200">
                  {confirmationCode}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(confirmationCode)}
                  className="text-green-600 hover:text-green-700 dark:text-green-400"
                >
                  {copySuccess ? (
                    <CheckCheck className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-green-600 dark:text-green-400">
                💾 Guarde este código para seus registros
              </p>
            </div>

            {/* Informações sobre os dados excluídos */}
            <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50">
              <Shield className="w-4 h-4 text-green-500" />
              <AlertDescription className="text-green-700 dark:text-green-300">
                <strong className="block mb-2">Dados Excluídos:</strong>
                <ul className="space-y-1 text-sm">
                  <li>✓ Informações do perfil do Instagram</li>
                  <li>✓ Tokens de acesso e autorização</li>
                  <li>✓ Dados de analytics e insights</li>
                  <li>✓ Cache de mídia e metadados</li>
                  <li>✓ Logs de atividades relacionadas</li>
                </ul>
              </AlertDescription>
            </Alert>

            <div className="text-center text-sm text-gray-600 dark:text-gray-400">
              <p>
                ⚖️ Em conformidade com LGPD e GDPR<br />
                🔒 Processo de exclusão concluído às {new Date().toLocaleString('pt-BR')}
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button 
              onClick={() => setShowSuccessDialog(false)}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              Entendi, Obrigado! 🎉
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 