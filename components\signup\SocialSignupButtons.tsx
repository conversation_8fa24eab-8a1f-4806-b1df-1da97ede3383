'use client'

import React, { useState } from 'react';
import { authService } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';

export const SocialSignupButtons: React.FC = () => {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);
  const { toast } = useToast();

  const handleSocialSignup = async (provider: 'google') => {
    try {
      setLoadingProvider(provider);

      const result = await authService.signInWithGoogle();

      if (result.success) {
        toast({
          title: "Sucesso!",
          description: result.message,
          variant: "default",
        });
      } else {
        toast({
          title: "Erro no login",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Erro inesperado",
        description: "Ocorreu um erro durante o login. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setLoadingProvider(null);
    }
  };

  return (
    <div className="w-full mb-8">
      <button
        onClick={() => handleSocialSignup('google')}
        disabled={loadingProvider !== null}
        className="w-full h-12 flex items-center rounded-full justify-center gap-3 bg-white border border-gray-300 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
        aria-label="Fazer login com o Google"
      >
        {loadingProvider === 'google' ? (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#4285F4]"></div>
        ) : (
          <>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19.9805 10.2051C19.9805 9.56847 19.9258 8.93951 19.8215 8.32422H10.2031V11.8448H15.6035C15.4023 12.9597 14.8047 13.9765 13.916 14.6853V17.1565H17.0176C18.9258 15.4238 19.9805 13.0508 19.9805 10.2051Z" fill="#4285F4"/>
              <path d="M10.2031 19.6094C12.9531 19.6094 15.2734 18.7324 17.0215 17.1563L13.9199 14.6855C13.1973 15.2168 12.2598 15.5332 10.2031 15.5332C7.5625 15.5332 5.31641 13.7871 4.50391 11.3926H1.29688V13.9434C3.08594 17.4824 6.44531 19.6094 10.2031 19.6094Z" fill="#34A853"/>
              <path d="M4.5 11.3925C4.11719 10.2776 4.11719 9.0639 4.5 7.94897V5.39819H1.29297C-0.429688 8.74272 -0.429688 12.5987 1.29297 15.9432L4.5 11.3925Z" fill="#FBBC04"/>
              <path d="M10.2031 4.46875C12.1406 4.44141 13.9922 5.18555 15.3359 6.44531L18.0547 3.80469C15.9766 1.875 13.1641 0.789063 10.2031 0.820313C6.44531 0.820313 3.08594 2.94727 1.29688 6.48633L4.50391 9.03711C5.31641 6.63477 7.5625 4.46875 10.2031 4.46875Z" fill="#EA4335"/>
            </svg>
            <span className="text-gray-700 font-medium">Fazer login com o Google</span>
          </>
        )}
      </button>
    </div>
  );
};
