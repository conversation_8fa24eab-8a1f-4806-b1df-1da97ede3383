import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

/**
 * Endpoint para Data Deletion Callback conforme requisitos do Instagram/Meta
 * POST /api/auth/data-deletion
 * 
 * Este endpoint recebe solicitações de exclusão de dados quando:
 * - <PERSON>u<PERSON><PERSON> remove o app das configurações do Instagram/Facebook
 * - Usuário solicita exclusão manual através da interface
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.formData();
    const signedRequest = body.get('signed_request') as string;

    if (!signedRequest) {
      return NextResponse.json({
        success: false,
        error: 'missing_signed_request',
        message: 'Parâmetro signed_request é obrigatório'
      }, { status: 400 });
    }

    // Parse do signed request do Instagram/Facebook
    const userData = parseSignedRequest(signedRequest);
    
    if (!userData) {
      return NextResponse.json({
        success: false,
        error: 'invalid_signed_request',
        message: 'Signed request inválido'
      }, { status: 400 });
    }

    const userId = userData.user_id;
    
    // Gera um código único de confirmação
    const confirmationCode = crypto.randomBytes(16).toString('hex');
    
    // Inicia o processo de exclusão de dados
    await deleteUserData(userId, confirmationCode);

    // URL onde o usuário pode verificar o status da exclusão
    const statusUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/auth/data-deletion/status?code=${confirmationCode}`;

    console.log(`🗑️ Solicitação de exclusão de dados iniciada para usuário: ${userId}`);
    console.log(`📋 Código de confirmação: ${confirmationCode}`);

    // Resposta conforme especificação do Instagram/Meta
    return NextResponse.json({
      url: statusUrl,
      confirmation_code: confirmationCode
    });

  } catch (error) {
    console.error('❌ Erro no processo de exclusão de dados:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * Endpoint GET para exclusão manual através da interface do usuário
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('user_id');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'missing_user_id',
        message: 'ID do usuário é obrigatório'
      }, { status: 400 });
    }

    // Gera um código único de confirmação
    const confirmationCode = crypto.randomBytes(16).toString('hex');
    
    // Inicia o processo de exclusão de dados
    await deleteUserData(userId, confirmationCode);

    // URL onde o usuário pode verificar o status da exclusão
    const statusUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/auth/data-deletion/status?code=${confirmationCode}`;

    console.log(`🗑️ Exclusão manual de dados iniciada para usuário: ${userId}`);
    console.log(`📋 Código de confirmação: ${confirmationCode}`);

    return NextResponse.json({
      success: true,
      message: 'Processo de exclusão de dados iniciado',
      confirmation_code: confirmationCode,
      status_url: statusUrl
    });

  } catch (error) {
    console.error('❌ Erro na exclusão manual de dados:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * Parse do signed request do Instagram/Facebook
 */
function parseSignedRequest(signedRequest: string) {
  try {
    const [encodedSig, payload] = signedRequest.split('.', 2);
    const secret = process.env.INSTAGRAM_CLIENT_SECRET;

    if (!secret) {
      throw new Error('INSTAGRAM_CLIENT_SECRET não configurado');
    }

    // Decodifica a assinatura e payload
    const sig = base64UrlDecode(encodedSig);
    const data = JSON.parse(base64UrlDecode(payload).toString());

    // Verifica a assinatura
    const expectedSig = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest();

    if (!crypto.timingSafeEqual(sig, expectedSig)) {
      throw new Error('Assinatura inválida no signed request');
    }

    return data;
  } catch (error) {
    console.error('❌ Erro ao fazer parse do signed request:', error);
    return null;
  }
}

/**
 * Decodifica base64 URL-safe
 */
function base64UrlDecode(input: string): Buffer {
  // Adiciona padding se necessário
  input += '='.repeat((4 - input.length % 4) % 4);
  // Substitui caracteres URL-safe
  input = input.replace(/-/g, '+').replace(/_/g, '/');
  return Buffer.from(input, 'base64');
}

/**
 * Processa a exclusão de dados do usuário
 */
async function deleteUserData(userId: string, confirmationCode: string) {
  try {
    console.log(`🗑️ Iniciando exclusão de dados para usuário: ${userId}`);

    // 1. Remove dados do localStorage (se houver uma forma de identificar)
    // Nota: localStorage é do lado cliente, então não podemos limpar diretamente aqui

    // 2. Remove dados do banco de dados (quando implementado)
    // await deleteFromDatabase(userId);

    // 3. Remove tokens de acesso e refresh tokens
    // await revokeAccessTokens(userId);

    // 4. Remove dados de analytics e insights salvos
    // await deleteAnalyticsData(userId);

    // 5. Remove dados de mídia em cache
    // await deleteMediaCache(userId);

    // 6. Registra o log de exclusão para auditoria
    await logDataDeletion(userId, confirmationCode);

    // 7. Notifica o usuário por email (opcional)
    // await sendDeletionConfirmationEmail(userId, confirmationCode);

    console.log(`✅ Dados do usuário ${userId} excluídos com sucesso`);
    console.log(`📋 Código de confirmação: ${confirmationCode}`);

  } catch (error) {
    console.error(`❌ Erro ao excluir dados do usuário ${userId}:`, error);
    throw error;
  }
}

/**
 * Registra o log de exclusão para auditoria
 */
async function logDataDeletion(userId: string, confirmationCode: string) {
  try {
    // Em um ambiente real, isso seria salvo no banco de dados
    const deletionRecord = {
      user_id: userId,
      confirmation_code: confirmationCode,
      deletion_requested_at: new Date().toISOString(),
      deletion_completed_at: new Date().toISOString(),
      status: 'completed',
      method: 'api_request',
      ip_address: '', // Seria capturado do request
      user_agent: '', // Seria capturado do request
    };

    console.log('📝 Registro de exclusão:', deletionRecord);

    // TODO: Salvar no banco de dados
    // await database.deletionLogs.create(deletionRecord);

  } catch (error) {
    console.error('❌ Erro ao registrar log de exclusão:', error);
    // Não falha o processo principal se o log falhar
  }
} 