"use client"

import React, { useEffect, useState } from 'react'
import { useTheme } from 'next-themes'

interface ThemeWrapperProps {
  children: React.ReactNode
}

/**
 * Componente wrapper que evita problemas de hidratação relacionados ao tema
 * Garante que a aplicação do tema aconteça apenas no cliente após a hidratação
 */
export function ThemeWrapper({ children }: ThemeWrapperProps) {
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  // Garante que a aplicação do tema só aconteça após a hidratação
  if (!mounted) {
    return <div suppressHydrationWarning>{children}</div>
  }

  return <>{children}</>
}

/**
 * Hook para usar o tema de forma segura, evitando problemas de hidratação
 */
export function useSafeTheme() {
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  return {
    theme: mounted ? theme : 'dark', // Fallback para dark durante a hidratação
    setTheme: mounted ? setTheme : () => {},
    mounted
  }
} 