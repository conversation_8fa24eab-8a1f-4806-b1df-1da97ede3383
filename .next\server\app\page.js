/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6156219662e3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5pc3RyYXRvclxcRG9jdW1lbnRzXFxhZmFjZS1jb21wYW55XFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjE1NjIxOTY2MmUzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-auth */ \"(rsc)/./hooks/use-auth.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'Aface ',\n    description: 'Conectando pessoas e criando experiências incríveis',\n    icons: {\n        icon: '/favicon.svg'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_3__.AppProviders, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_use_auth__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_5__.Toaster, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\layout.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_6__.Toaster, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\nfunction HomePage() {\n    // Redireciona automaticamente para a página de login\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect)('/sign-in');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEM7QUFFM0IsU0FBU0M7SUFDdEIscURBQXFEO0lBQ3JERCx5REFBUUEsQ0FBQztBQUNYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERvY3VtZW50c1xcYWZhY2UtY29tcGFueVxcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZWRpcmVjdCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWVQYWdlKCkge1xyXG4gIC8vIFJlZGlyZWNpb25hIGF1dG9tYXRpY2FtZW50ZSBwYXJhIGEgcMOhZ2luYSBkZSBsb2dpblxyXG4gIHJlZGlyZWN0KCcvc2lnbi1pbicpXHJcbn0gIl0sIm5hbWVzIjpbInJlZGlyZWN0IiwiSG9tZVBhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppProviders: () => (/* binding */ AppProviders),
/* harmony export */   AppThemeProvider: () => (/* binding */ AppThemeProvider),
/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const ReactQueryProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\app\\providers.tsx",
"ReactQueryProvider",
);const AppThemeProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AppThemeProvider() from the server but AppThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\app\\providers.tsx",
"AppThemeProvider",
);const AppProviders = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AppProviders() from the server but AppProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\app\\providers.tsx",
"AppProviders",
);

/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster),
/* harmony export */   toast: () => (/* binding */ toast)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sonner.tsx",
"Toaster",
);const toast = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\sonner.tsx",
"toast",
);

/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\components\\ui\\toaster.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./hooks/use-auth.tsx":
/*!****************************!*\
  !*** ./hooks/use-auth.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\hooks\\use-auth.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\aface-company\\hooks\\use-auth.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q0FkbWluaXN0cmF0b3IlNUNEb2N1bWVudHMlNUNhZmFjZS1jb21wYW55JTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNBZG1pbmlzdHJhdG9yJTVDRG9jdW1lbnRzJTVDYWZhY2UtY29tcGFueSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCZpc0dsb2JhbE5vdEZvdW5kRW5hYmxlZD0hIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQiw0SUFBd0c7QUFDOUgsc0JBQXNCLHVPQUF3RjtBQUM5RyxzQkFBc0IsaU9BQXFGO0FBQzNHLHNCQUFzQixpT0FBcUY7QUFDM0csc0JBQXNCLHVPQUF3RjtBQUM5RyxvQkFBb0Isd0lBQXNHO0FBR3hIO0FBR0E7QUFDMkU7QUFDTDtBQUNUO0FBQ087QUFDTztBQUNPO0FBQ1A7QUFDSztBQUNZO0FBQ1c7QUFDeEI7QUFDRjtBQUNhO0FBQ2lFO0FBQ2hGO0FBQ1g7QUFDUTtBQUNoQjtBQUN1QjtBQUNQO0FBQ1Q7QUFDaUI7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNxQjtBQUN2Qiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNvRjtBQUdwRjtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYSxPQUFvQyxJQUFJLENBQUU7QUFDdkQsZ0JBQWdCLE1BQXVDO0FBQ3ZELENBQUM7QUFDTTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLEtBQXFCLEVBQUUsRUFFMUIsQ0FBQztBQUNOO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixPQUF3QztBQUN2RSw2QkFBNkIsNkVBQWM7QUFDM0M7QUFDQSx3QkFBd0IsNkVBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVkscVNBQXFTO0FBQ2pUO0FBQ0EsOEJBQThCLDhGQUFnQjtBQUM5QyxVQUFVLHVCQUF1QjtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixxRkFBVTtBQUM5QixzQkFBc0IsMEZBQWdCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw2RUFBYyxxREFBcUQsd0dBQTJCO0FBQ2pJO0FBQ0EseUJBQXlCLDZFQUFjLDZDQUE2Qyx1RkFBVTtBQUM5RixtQ0FBbUMsMkdBQXlCO0FBQzVEO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwyRkFBb0I7QUFDbEQ7QUFDQTtBQUNBLHFDQUFxQyxNQUE0RyxJQUFJLENBQWU7QUFDcEs7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQSxrQ0FBa0MsNkVBQWM7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsc0dBQTRCO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9FQUFTO0FBQ3BCO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNkdBQThCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixnR0FBcUI7QUFDbEQ7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSxtQkFBbUIsNEVBQVM7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDRFQUFlO0FBQy9DLGdDQUFnQyw2RUFBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLElBQXNDO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxnRkFBYztBQUMvRSwrREFBK0QseUNBQXlDO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLFFBQVEsRUFBRSxNQUFNO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0Esa0JBQWtCO0FBQ2xCLHVDQUF1QyxRQUFRLEVBQUUsUUFBUTtBQUN6RDtBQUNBLGFBQWE7QUFDYjtBQUNBLGtDQUFrQyxzQ0FBc0M7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLDBDQUEwQyw2RUFBYztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBLCtCQUErQiwyRkFBYztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw2RUFBYztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsSUFBSTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsNENBQTRDO0FBQzVDO0FBQ0EseUJBQXlCLDZFQUFjO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFdBQVc7QUFDL0Isb0JBQW9CLDBCQUEwQjtBQUM5QyxtQ0FBbUM7QUFDbkM7QUFDQSx3QkFBd0IsNEVBQXNCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhHQUE4RyxpQkFBaUIsRUFBRSxvRkFBb0YsOEJBQThCLE9BQU87QUFDMVA7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsNkVBQWU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyx1REFBdUQ7QUFDbEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsMkVBQWtCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGlFQUFZLGNBQWMsZ0ZBQUs7QUFDaEUsK0JBQStCLGlFQUFZO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELGlFQUFZO0FBQ3RFLCtCQUErQixpRUFBWTtBQUMzQztBQUNBLGlEQUFpRCxpRUFBWTtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGlFQUFZO0FBQzdDLDhCQUE4Qiw2RkFBZTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsa0VBQVM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVFQUF1RSxnR0FBc0I7QUFDN0YsNkJBQTZCO0FBQzdCO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLDhCQUE4Qiw2RUFBZTtBQUM3Qyw4QkFBOEIsdUVBQVk7QUFDMUMsb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRUFBK0UsNkVBQWMsd0RBQXdELGdHQUFzQjtBQUMzSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsMkJBQTJCLGtFQUFTO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsdUdBQXVHLDZFQUFlO0FBQ3RIO0FBQ0EsaUhBQWlILG1GQUFtRjtBQUNwTTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIscUdBQXdCO0FBQ3REO0FBQ0Esb0JBQW9CLG9CQUFvQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0hBQWdILG9DQUFvQztBQUNwSjtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBLHdDQUF3QyxvRUFBYztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpSEFBaUgsNkVBQWU7QUFDaEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIscUdBQXdCO0FBQ3REO0FBQ0E7QUFDQSxpSEFBaUgsNEVBQXNCO0FBQ3ZJO0FBQ0Esa0NBQWtDLDRFQUFzQjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixnRkFBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyx1RUFBWTtBQUM1QztBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdGQUFnQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHVFQUFZO0FBQ3hDO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyw2RUFBYztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIseUJBQXlCLDZFQUFjO0FBQ3ZDLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLDRFQUFzQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJHQUEyRyw0RUFBc0I7QUFDakk7QUFDQSw4QkFBOEIsNEVBQXNCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsaUdBQWtCO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHFHQUF3QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBLDJCQUEyQixnRkFBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdGQUFnQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHVFQUFZO0FBQ3hDO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdGQUFnQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxxRkFBWTtBQUN2RDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLCtGQUErRiw2RUFBZTtBQUM5RztBQUNBLHNHQUFzRyx1RUFBdUU7QUFDN0s7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2IsbUJBQW1CLGdGQUFnQjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLG9GQUFvRixnRkFBYztBQUNsRyxpQ0FBaUMsUUFBUSxFQUFFLFFBQVE7QUFDbkQsMEJBQTBCLHVFQUFRO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQywyRkFBbUI7QUFDckQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFkbWluaXN0cmF0b3JcXFxcRG9jdW1lbnRzXFxcXGFmYWNlLWNvbXBhbnlcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9nbG9iYWwtZXJyb3IuanNcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9ub3QtZm91bmQuanNcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9mb3JiaWRkZW4uanNcIik7XG5jb25zdCBtb2R1bGU0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi91bmF1dGhvcml6ZWQuanNcIik7XG5jb25zdCBwYWdlNSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5pc3RyYXRvclxcXFxEb2N1bWVudHNcXFxcYWZhY2UtY29tcGFueVxcXFxhcHBcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuaW1wb3J0IHsgZ2V0UmV2YWxpZGF0ZVJlYXNvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2luc3RydW1lbnRhdGlvbi91dGlsc1wiO1xuaW1wb3J0IHsgZ2V0VHJhY2VyLCBTcGFuS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS90cmFjZXJcIjtcbmltcG9ydCB7IGdldFJlcXVlc3RNZXRhIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVxdWVzdC1tZXRhXCI7XG5pbXBvcnQgeyBCYXNlU2VydmVyU3BhbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS9jb25zdGFudHNcIjtcbmltcG9ydCB7IGludGVyb3BEZWZhdWx0IH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9pbnRlcm9wLWRlZmF1bHRcIjtcbmltcG9ydCB7IE5vZGVOZXh0UmVxdWVzdCwgTm9kZU5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Jhc2UtaHR0cC9ub2RlXCI7XG5pbXBvcnQgeyBjaGVja0lzQXBwUFBSRW5hYmxlZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9leHBlcmltZW50YWwvcHByXCI7XG5pbXBvcnQgeyBnZXRGYWxsYmFja1JvdXRlUGFyYW1zIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVxdWVzdC9mYWxsYmFjay1wYXJhbXNcIjtcbmltcG9ydCB7IHNldFJlZmVyZW5jZU1hbmlmZXN0c1NpbmdsZXRvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW5jcnlwdGlvbi11dGlsc1wiO1xuaW1wb3J0IHsgaXNIdG1sQm90UmVxdWVzdCwgc2hvdWxkU2VydmVTdHJlYW1pbmdNZXRhZGF0YSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9zdHJlYW1pbmctbWV0YWRhdGFcIjtcbmltcG9ydCB7IGNyZWF0ZVNlcnZlck1vZHVsZU1hcCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWN0aW9uLXV0aWxzXCI7XG5pbXBvcnQgeyBub3JtYWxpemVBcHBQYXRoIH0gZnJvbSBcIm5leHQvZGlzdC9zaGFyZWQvbGliL3JvdXRlci91dGlscy9hcHAtcGF0aHNcIjtcbmltcG9ydCB7IGdldElzUG9zc2libGVTZXJ2ZXJBY3Rpb24gfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvc2VydmVyLWFjdGlvbi1yZXF1ZXN0LW1ldGFcIjtcbmltcG9ydCB7IFJTQ19IRUFERVIsIE5FWFRfUk9VVEVSX1BSRUZFVENIX0hFQURFUiwgTkVYVF9JU19QUkVSRU5ERVJfSEVBREVSLCBORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXItaGVhZGVyc1wiO1xuaW1wb3J0IHsgZ2V0Qm90VHlwZSwgaXNCb3QgfSBmcm9tIFwibmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWJvdFwiO1xuaW1wb3J0IHsgQ2FjaGVkUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVzcG9uc2UtY2FjaGVcIjtcbmltcG9ydCB7IEZhbGxiYWNrTW9kZSwgcGFyc2VGYWxsYmFja0ZpZWxkIH0gZnJvbSBcIm5leHQvZGlzdC9saWIvZmFsbGJhY2tcIjtcbmltcG9ydCBSZW5kZXJSZXN1bHQgZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVuZGVyLXJlc3VsdFwiO1xuaW1wb3J0IHsgQ0FDSEVfT05FX1lFQVIsIE5FWFRfQ0FDSEVfVEFHU19IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2xpYi9jb25zdGFudHNcIjtcbmltcG9ydCB7IEVOQ09ERURfVEFHUyB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3N0cmVhbS11dGlscy9lbmNvZGVkLXRhZ3NcIjtcbmltcG9ydCB7IHNlbmRSZW5kZXJSZXN1bHQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9zZW5kLXBheWxvYWRcIjtcbmltcG9ydCB7IE5vRmFsbGJhY2tFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3Qvc2hhcmVkL2xpYi9uby1mYWxsYmFjay1lcnJvci5leHRlcm5hbFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNSwgXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbmlzdHJhdG9yXFxcXERvY3VtZW50c1xcXFxhZmFjZS1jb21wYW55XFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkM6XFxcXFVzZXJzXFxcXEFkbWluaXN0cmF0b3JcXFxcRG9jdW1lbnRzXFxcXGFmYWNlLWNvbXBhbnlcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nZ2xvYmFsLWVycm9yJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vZ2xvYmFsLWVycm9yLmpzXCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL25vdC1mb3VuZC5qc1wiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9mb3JiaWRkZW4uanNcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTQsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vdW5hdXRob3JpemVkLmpzXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXEFkbWluaXN0cmF0b3JcXFxcRG9jdW1lbnRzXFxcXGFmYWNlLWNvbXBhbnlcXFxcYXBwXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmltcG9ydCBHbG9iYWxFcnJvciBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vZ2xvYmFsLWVycm9yLmpzXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuZXhwb3J0IHsgR2xvYmFsRXJyb3IgfTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuaW1wb3J0ICogYXMgZW50cnlCYXNlIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmltcG9ydCB7IFJlZGlyZWN0U3RhdHVzQ29kZSB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVkaXJlY3Qtc3RhdHVzLWNvZGVcIjtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH0sXG4gICAgZGlzdERpcjogcHJvY2Vzcy5lbnYuX19ORVhUX1JFTEFUSVZFX0RJU1RfRElSIHx8ICcnLFxuICAgIHByb2plY3REaXI6IHByb2Nlc3MuZW52Ll9fTkVYVF9SRUxBVElWRV9QUk9KRUNUX0RJUiB8fCAnJ1xufSk7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gaGFuZGxlcihyZXEsIHJlcywgY3R4KSB7XG4gICAgdmFyIF90aGlzO1xuICAgIGxldCBzcmNQYWdlID0gXCIvcGFnZVwiO1xuICAgIC8vIHR1cmJvcGFjayBkb2Vzbid0IG5vcm1hbGl6ZSBgL2luZGV4YCBpbiB0aGUgcGFnZSBuYW1lXG4gICAgLy8gc28gd2UgbmVlZCB0byB0byBwcm9jZXNzIGR5bmFtaWMgcm91dGVzIHByb3Blcmx5XG4gICAgLy8gVE9ETzogZml4IHR1cmJvcGFjayBwcm92aWRpbmcgZGlmZmVyaW5nIHZhbHVlIGZyb20gd2VicGFja1xuICAgIGlmIChwcm9jZXNzLmVudi5UVVJCT1BBQ0spIHtcbiAgICAgICAgc3JjUGFnZSA9IHNyY1BhZ2UucmVwbGFjZSgvXFwvaW5kZXgkLywgJycpIHx8ICcvJztcbiAgICB9IGVsc2UgaWYgKHNyY1BhZ2UgPT09ICcvaW5kZXgnKSB7XG4gICAgICAgIC8vIHdlIGFsd2F5cyBub3JtYWxpemUgL2luZGV4IHNwZWNpZmljYWxseVxuICAgICAgICBzcmNQYWdlID0gJy8nO1xuICAgIH1cbiAgICBjb25zdCBtdWx0aVpvbmVEcmFmdE1vZGUgPSBwcm9jZXNzLmVudi5fX05FWFRfTVVMVElfWk9ORV9EUkFGVF9NT0RFO1xuICAgIGNvbnN0IGluaXRpYWxQb3N0cG9uZWQgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdwb3N0cG9uZWQnKTtcbiAgICAvLyBUT0RPOiByZXBsYWNlIHdpdGggbW9yZSBzcGVjaWZpYyBmbGFnc1xuICAgIGNvbnN0IG1pbmltYWxNb2RlID0gZ2V0UmVxdWVzdE1ldGEocmVxLCAnbWluaW1hbE1vZGUnKTtcbiAgICBjb25zdCBwcmVwYXJlUmVzdWx0ID0gYXdhaXQgcm91dGVNb2R1bGUucHJlcGFyZShyZXEsIHJlcywge1xuICAgICAgICBzcmNQYWdlLFxuICAgICAgICBtdWx0aVpvbmVEcmFmdE1vZGVcbiAgICB9KTtcbiAgICBpZiAoIXByZXBhcmVSZXN1bHQpIHtcbiAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSA0MDA7XG4gICAgICAgIHJlcy5lbmQoJ0JhZCBSZXF1ZXN0Jyk7XG4gICAgICAgIGN0eC53YWl0VW50aWwgPT0gbnVsbCA/IHZvaWQgMCA6IGN0eC53YWl0VW50aWwuY2FsbChjdHgsIFByb21pc2UucmVzb2x2ZSgpKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGNvbnN0IHsgYnVpbGRJZCwgcXVlcnksIHBhcmFtcywgcGFyc2VkVXJsLCBwYWdlSXNEeW5hbWljLCBidWlsZE1hbmlmZXN0LCBuZXh0Rm9udE1hbmlmZXN0LCByZWFjdExvYWRhYmxlTWFuaWZlc3QsIHNlcnZlckFjdGlvbnNNYW5pZmVzdCwgY2xpZW50UmVmZXJlbmNlTWFuaWZlc3QsIHN1YnJlc291cmNlSW50ZWdyaXR5TWFuaWZlc3QsIHByZXJlbmRlck1hbmlmZXN0LCBpc0RyYWZ0TW9kZSwgcmVzb2x2ZWRQYXRobmFtZSwgcmV2YWxpZGF0ZU9ubHlHZW5lcmF0ZWQsIHJvdXRlclNlcnZlckNvbnRleHQsIG5leHRDb25maWcgfSA9IHByZXBhcmVSZXN1bHQ7XG4gICAgY29uc3QgcGF0aG5hbWUgPSBwYXJzZWRVcmwucGF0aG5hbWUgfHwgJy8nO1xuICAgIGNvbnN0IG5vcm1hbGl6ZWRTcmNQYWdlID0gbm9ybWFsaXplQXBwUGF0aChzcmNQYWdlKTtcbiAgICBsZXQgeyBpc09uRGVtYW5kUmV2YWxpZGF0ZSB9ID0gcHJlcGFyZVJlc3VsdDtcbiAgICBjb25zdCBwcmVyZW5kZXJJbmZvID0gcHJlcmVuZGVyTWFuaWZlc3QuZHluYW1pY1JvdXRlc1tub3JtYWxpemVkU3JjUGFnZV07XG4gICAgY29uc3QgaXNQcmVyZW5kZXJlZCA9IHByZXJlbmRlck1hbmlmZXN0LnJvdXRlc1tyZXNvbHZlZFBhdGhuYW1lXTtcbiAgICBsZXQgaXNTU0cgPSBCb29sZWFuKHByZXJlbmRlckluZm8gfHwgaXNQcmVyZW5kZXJlZCB8fCBwcmVyZW5kZXJNYW5pZmVzdC5yb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdKTtcbiAgICBjb25zdCB1c2VyQWdlbnQgPSByZXEuaGVhZGVyc1sndXNlci1hZ2VudCddIHx8ICcnO1xuICAgIGNvbnN0IGJvdFR5cGUgPSBnZXRCb3RUeXBlKHVzZXJBZ2VudCk7XG4gICAgY29uc3QgaXNIdG1sQm90ID0gaXNIdG1sQm90UmVxdWVzdChyZXEpO1xuICAgIC8qKlxuICAgKiBJZiB0cnVlLCB0aGlzIGluZGljYXRlcyB0aGF0IHRoZSByZXF1ZXN0IGJlaW5nIG1hZGUgaXMgZm9yIGFuIGFwcFxuICAgKiBwcmVmZXRjaCByZXF1ZXN0LlxuICAgKi8gY29uc3QgaXNQcmVmZXRjaFJTQ1JlcXVlc3QgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdpc1ByZWZldGNoUlNDUmVxdWVzdCcpID8/IEJvb2xlYW4ocmVxLmhlYWRlcnNbTkVYVF9ST1VURVJfUFJFRkVUQ0hfSEVBREVSXSk7XG4gICAgLy8gTk9URTogRG9uJ3QgZGVsZXRlIGhlYWRlcnNbUlNDXSB5ZXQsIGl0IHN0aWxsIG5lZWRzIHRvIGJlIHVzZWQgaW4gcmVuZGVyVG9IVE1MIGxhdGVyXG4gICAgY29uc3QgaXNSU0NSZXF1ZXN0ID0gZ2V0UmVxdWVzdE1ldGEocmVxLCAnaXNSU0NSZXF1ZXN0JykgPz8gQm9vbGVhbihyZXEuaGVhZGVyc1tSU0NfSEVBREVSXSk7XG4gICAgY29uc3QgaXNQb3NzaWJsZVNlcnZlckFjdGlvbiA9IGdldElzUG9zc2libGVTZXJ2ZXJBY3Rpb24ocmVxKTtcbiAgICAvKipcbiAgICogSWYgdGhlIHJvdXRlIGJlaW5nIHJlbmRlcmVkIGlzIGFuIGFwcCBwYWdlLCBhbmQgdGhlIHBwciBmZWF0dXJlIGhhcyBiZWVuXG4gICAqIGVuYWJsZWQsIHRoZW4gdGhlIGdpdmVuIHJvdXRlIF9jb3VsZF8gc3VwcG9ydCBQUFIuXG4gICAqLyBjb25zdCBjb3VsZFN1cHBvcnRQUFIgPSBjaGVja0lzQXBwUFBSRW5hYmxlZChuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5wcHIpO1xuICAgIC8vIFdoZW4gZW5hYmxlZCwgdGhpcyB3aWxsIGFsbG93IHRoZSB1c2Ugb2YgdGhlIGA/X19uZXh0cHByb25seWAgcXVlcnkgdG9cbiAgICAvLyBlbmFibGUgZGVidWdnaW5nIG9mIHRoZSBzdGF0aWMgc2hlbGwuXG4gICAgY29uc3QgaGFzRGVidWdTdGF0aWNTaGVsbFF1ZXJ5ID0gcHJvY2Vzcy5lbnYuX19ORVhUX0VYUEVSSU1FTlRBTF9TVEFUSUNfU0hFTExfREVCVUdHSU5HID09PSAnMScgJiYgdHlwZW9mIHF1ZXJ5Ll9fbmV4dHBwcm9ubHkgIT09ICd1bmRlZmluZWQnICYmIGNvdWxkU3VwcG9ydFBQUjtcbiAgICAvLyBXaGVuIGVuYWJsZWQsIHRoaXMgd2lsbCBhbGxvdyB0aGUgdXNlIG9mIHRoZSBgP19fbmV4dHBwcm9ubHlgIHF1ZXJ5XG4gICAgLy8gdG8gZW5hYmxlIGRlYnVnZ2luZyBvZiB0aGUgZmFsbGJhY2sgc2hlbGwuXG4gICAgY29uc3QgaGFzRGVidWdGYWxsYmFja1NoZWxsUXVlcnkgPSBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgJiYgcXVlcnkuX19uZXh0cHByb25seSA9PT0gJ2ZhbGxiYWNrJztcbiAgICAvLyBUaGlzIHBhZ2Ugc3VwcG9ydHMgUFBSIGlmIGl0IGlzIG1hcmtlZCBhcyBiZWluZyBgUEFSVElBTExZX1NUQVRJQ2AgaW4gdGhlXG4gICAgLy8gcHJlcmVuZGVyIG1hbmlmZXN0IGFuZCB0aGlzIGlzIGFuIGFwcCBwYWdlLlxuICAgIGNvbnN0IGlzUm91dGVQUFJFbmFibGVkID0gY291bGRTdXBwb3J0UFBSICYmICgoKF90aGlzID0gcHJlcmVuZGVyTWFuaWZlc3Qucm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXSA/PyBwcmVyZW5kZXJNYW5pZmVzdC5keW5hbWljUm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXSkgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzLnJlbmRlcmluZ01vZGUpID09PSAnUEFSVElBTExZX1NUQVRJQycgfHwgLy8gSWRlYWxseSB3ZSdkIHdhbnQgdG8gY2hlY2sgdGhlIGFwcENvbmZpZyB0byBzZWUgaWYgdGhpcyBwYWdlIGhhcyBQUFJcbiAgICAvLyBlbmFibGVkIG9yIG5vdCwgYnV0IHRoYXQgd291bGQgcmVxdWlyZSBwbHVtYmluZyB0aGUgYXBwQ29uZmlnIHRocm91Z2hcbiAgICAvLyB0byB0aGUgc2VydmVyIGR1cmluZyBkZXZlbG9wbWVudC4gV2UgYXNzdW1lIHRoYXQgdGhlIHBhZ2Ugc3VwcG9ydHMgaXRcbiAgICAvLyBidXQgb25seSBkdXJpbmcgZGV2ZWxvcG1lbnQuXG4gICAgaGFzRGVidWdTdGF0aWNTaGVsbFF1ZXJ5ICYmIChyb3V0ZU1vZHVsZS5pc0RldiA9PT0gdHJ1ZSB8fCAocm91dGVyU2VydmVyQ29udGV4dCA9PSBudWxsID8gdm9pZCAwIDogcm91dGVyU2VydmVyQ29udGV4dC5leHBlcmltZW50YWxUZXN0UHJveHkpID09PSB0cnVlKSk7XG4gICAgY29uc3QgaXNEZWJ1Z1N0YXRpY1NoZWxsID0gaGFzRGVidWdTdGF0aWNTaGVsbFF1ZXJ5ICYmIGlzUm91dGVQUFJFbmFibGVkO1xuICAgIC8vIFdlIHNob3VsZCBlbmFibGUgZGVidWdnaW5nIGR5bmFtaWMgYWNjZXNzZXMgd2hlbiB0aGUgc3RhdGljIHNoZWxsXG4gICAgLy8gZGVidWdnaW5nIGhhcyBiZWVuIGVuYWJsZWQgYW5kIHdlJ3JlIGFsc28gaW4gZGV2ZWxvcG1lbnQgbW9kZS5cbiAgICBjb25zdCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzID0gaXNEZWJ1Z1N0YXRpY1NoZWxsICYmIHJvdXRlTW9kdWxlLmlzRGV2ID09PSB0cnVlO1xuICAgIGNvbnN0IGlzRGVidWdGYWxsYmFja1NoZWxsID0gaGFzRGVidWdGYWxsYmFja1NoZWxsUXVlcnkgJiYgaXNSb3V0ZVBQUkVuYWJsZWQ7XG4gICAgLy8gSWYgd2UncmUgaW4gbWluaW1hbCBtb2RlLCB0aGVuIHRyeSB0byBnZXQgdGhlIHBvc3Rwb25lZCBpbmZvcm1hdGlvbiBmcm9tXG4gICAgLy8gdGhlIHJlcXVlc3QgbWV0YWRhdGEuIElmIGF2YWlsYWJsZSwgdXNlIGl0IGZvciByZXN1bWluZyB0aGUgcG9zdHBvbmVkXG4gICAgLy8gcmVuZGVyLlxuICAgIGNvbnN0IG1pbmltYWxQb3N0cG9uZWQgPSBpc1JvdXRlUFBSRW5hYmxlZCA/IGluaXRpYWxQb3N0cG9uZWQgOiB1bmRlZmluZWQ7XG4gICAgLy8gSWYgUFBSIGlzIGVuYWJsZWQsIGFuZCB0aGlzIGlzIGEgUlNDIHJlcXVlc3QgKGJ1dCBub3QgYSBwcmVmZXRjaCksIHRoZW5cbiAgICAvLyB3ZSBjYW4gdXNlIHRoaXMgZmFjdCB0byBvbmx5IGdlbmVyYXRlIHRoZSBmbGlnaHQgZGF0YSBmb3IgdGhlIHJlcXVlc3RcbiAgICAvLyBiZWNhdXNlIHdlIGNhbid0IGNhY2hlIHRoZSBIVE1MIChhcyBpdCdzIGFsc28gZHluYW1pYykuXG4gICAgY29uc3QgaXNEeW5hbWljUlNDUmVxdWVzdCA9IGlzUm91dGVQUFJFbmFibGVkICYmIGlzUlNDUmVxdWVzdCAmJiAhaXNQcmVmZXRjaFJTQ1JlcXVlc3Q7XG4gICAgLy8gTmVlZCB0byByZWFkIHRoaXMgYmVmb3JlIGl0J3Mgc3RyaXBwZWQgYnkgc3RyaXBGbGlnaHRIZWFkZXJzLiBXZSBkb24ndFxuICAgIC8vIG5lZWQgdG8gdHJhbnNmZXIgaXQgdG8gdGhlIHJlcXVlc3QgbWV0YSBiZWNhdXNlIGl0J3Mgb25seSByZWFkXG4gICAgLy8gd2l0aGluIHRoaXMgZnVuY3Rpb247IHRoZSBzdGF0aWMgc2VnbWVudCBkYXRhIHNob3VsZCBoYXZlIGFscmVhZHkgYmVlblxuICAgIC8vIGdlbmVyYXRlZCwgc28gd2Ugd2lsbCBhbHdheXMgZWl0aGVyIHJldHVybiBhIHN0YXRpYyByZXNwb25zZSBvciBhIDQwNC5cbiAgICBjb25zdCBzZWdtZW50UHJlZmV0Y2hIZWFkZXIgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdzZWdtZW50UHJlZmV0Y2hSU0NSZXF1ZXN0Jyk7XG4gICAgLy8gVE9ETzogaW52ZXN0aWdhdGUgZXhpc3RpbmcgYnVnIHdpdGggc2hvdWxkU2VydmVTdHJlYW1pbmdNZXRhZGF0YSBhbHdheXNcbiAgICAvLyBiZWluZyB0cnVlIGZvciBhIHJldmFsaWRhdGUgZHVlIHRvIG1vZGlmeWluZyB0aGUgYmFzZS1zZXJ2ZXIgdGhpcy5yZW5kZXJPcHRzXG4gICAgLy8gd2hlbiBmaXhpbmcgdGhpcyB0byBjb3JyZWN0IGxvZ2ljIGl0IGNhdXNlcyBoeWRyYXRpb24gaXNzdWUgc2luY2Ugd2Ugc2V0XG4gICAgLy8gc2VydmVTdHJlYW1pbmdNZXRhZGF0YSB0byB0cnVlIGR1cmluZyBleHBvcnRcbiAgICBsZXQgc2VydmVTdHJlYW1pbmdNZXRhZGF0YSA9ICF1c2VyQWdlbnQgPyB0cnVlIDogc2hvdWxkU2VydmVTdHJlYW1pbmdNZXRhZGF0YSh1c2VyQWdlbnQsIG5leHRDb25maWcuaHRtbExpbWl0ZWRCb3RzKTtcbiAgICBpZiAoaXNIdG1sQm90ICYmIGlzUm91dGVQUFJFbmFibGVkKSB7XG4gICAgICAgIGlzU1NHID0gZmFsc2U7XG4gICAgICAgIHNlcnZlU3RyZWFtaW5nTWV0YWRhdGEgPSBmYWxzZTtcbiAgICB9XG4gICAgLy8gSW4gZGV2ZWxvcG1lbnQsIHdlIGFsd2F5cyB3YW50IHRvIGdlbmVyYXRlIGR5bmFtaWMgSFRNTC5cbiAgICBsZXQgc3VwcG9ydHNEeW5hbWljUmVzcG9uc2UgPSAvLyBJZiB3ZSdyZSBpbiBkZXZlbG9wbWVudCwgd2UgYWx3YXlzIHN1cHBvcnQgZHluYW1pYyBIVE1MLCB1bmxlc3MgaXQnc1xuICAgIC8vIGEgZGF0YSByZXF1ZXN0LCBpbiB3aGljaCBjYXNlIHdlIG9ubHkgcHJvZHVjZSBzdGF0aWMgSFRNTC5cbiAgICByb3V0ZU1vZHVsZS5pc0RldiA9PT0gdHJ1ZSB8fCAvLyBJZiB0aGlzIGlzIG5vdCBTU0cgb3IgZG9lcyBub3QgaGF2ZSBzdGF0aWMgcGF0aHMsIHRoZW4gaXQgc3VwcG9ydHNcbiAgICAvLyBkeW5hbWljIEhUTUwuXG4gICAgIWlzU1NHIHx8IC8vIElmIHRoaXMgcmVxdWVzdCBoYXMgcHJvdmlkZWQgcG9zdHBvbmVkIGRhdGEsIGl0IHN1cHBvcnRzIGR5bmFtaWNcbiAgICAvLyBIVE1MLlxuICAgIHR5cGVvZiBpbml0aWFsUG9zdHBvbmVkID09PSAnc3RyaW5nJyB8fCAvLyBJZiB0aGlzIGlzIGEgZHluYW1pYyBSU0MgcmVxdWVzdCwgdGhlbiB0aGlzIHJlbmRlciBzdXBwb3J0cyBkeW5hbWljXG4gICAgLy8gSFRNTCAoaXQncyBkeW5hbWljKS5cbiAgICBpc0R5bmFtaWNSU0NSZXF1ZXN0O1xuICAgIC8vIFdoZW4gaHRtbCBib3RzIHJlcXVlc3QgUFBSIHBhZ2UsIHBlcmZvcm0gdGhlIGZ1bGwgZHluYW1pYyByZW5kZXJpbmcuXG4gICAgY29uc3Qgc2hvdWxkV2FpdE9uQWxsUmVhZHkgPSBpc0h0bWxCb3QgJiYgaXNSb3V0ZVBQUkVuYWJsZWQ7XG4gICAgbGV0IHNzZ0NhY2hlS2V5ID0gbnVsbDtcbiAgICBpZiAoIWlzRHJhZnRNb2RlICYmIGlzU1NHICYmICFzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSAmJiAhaXNQb3NzaWJsZVNlcnZlckFjdGlvbiAmJiAhbWluaW1hbFBvc3Rwb25lZCAmJiAhaXNEeW5hbWljUlNDUmVxdWVzdCkge1xuICAgICAgICBzc2dDYWNoZUtleSA9IHJlc29sdmVkUGF0aG5hbWU7XG4gICAgfVxuICAgIC8vIHRoZSBzdGF0aWNQYXRoS2V5IGRpZmZlcnMgZnJvbSBzc2dDYWNoZUtleSBzaW5jZVxuICAgIC8vIHNzZ0NhY2hlS2V5IGlzIG51bGwgaW4gZGV2IHNpbmNlIHdlJ3JlIGFsd2F5cyBpbiBcImR5bmFtaWNcIlxuICAgIC8vIG1vZGUgaW4gZGV2IHRvIGJ5cGFzcyB0aGUgY2FjaGUsIGJ1dCB3ZSBzdGlsbCBuZWVkIHRvIGhvbm9yXG4gICAgLy8gZHluYW1pY1BhcmFtcyA9IGZhbHNlIGluIGRldiBtb2RlXG4gICAgbGV0IHN0YXRpY1BhdGhLZXkgPSBzc2dDYWNoZUtleTtcbiAgICBpZiAoIXN0YXRpY1BhdGhLZXkgJiYgcm91dGVNb2R1bGUuaXNEZXYpIHtcbiAgICAgICAgc3RhdGljUGF0aEtleSA9IHJlc29sdmVkUGF0aG5hbWU7XG4gICAgfVxuICAgIGNvbnN0IENvbXBvbmVudE1vZCA9IHtcbiAgICAgICAgLi4uZW50cnlCYXNlLFxuICAgICAgICB0cmVlLFxuICAgICAgICBwYWdlcyxcbiAgICAgICAgR2xvYmFsRXJyb3IsXG4gICAgICAgIGhhbmRsZXIsXG4gICAgICAgIHJvdXRlTW9kdWxlLFxuICAgICAgICBfX25leHRfYXBwX19cbiAgICB9O1xuICAgIC8vIEJlZm9yZSByZW5kZXJpbmcgKHdoaWNoIGluaXRpYWxpemVzIGNvbXBvbmVudCB0cmVlIG1vZHVsZXMpLCB3ZSBoYXZlIHRvXG4gICAgLy8gc2V0IHRoZSByZWZlcmVuY2UgbWFuaWZlc3RzIHRvIG91ciBnbG9iYWwgc3RvcmUgc28gU2VydmVyIEFjdGlvbidzXG4gICAgLy8gZW5jcnlwdGlvbiB1dGlsIGNhbiBhY2Nlc3MgdG8gdGhlbSBhdCB0aGUgdG9wIGxldmVsIG9mIHRoZSBwYWdlIG1vZHVsZS5cbiAgICBpZiAoc2VydmVyQWN0aW9uc01hbmlmZXN0ICYmIGNsaWVudFJlZmVyZW5jZU1hbmlmZXN0KSB7XG4gICAgICAgIHNldFJlZmVyZW5jZU1hbmlmZXN0c1NpbmdsZXRvbih7XG4gICAgICAgICAgICBwYWdlOiBzcmNQYWdlLFxuICAgICAgICAgICAgY2xpZW50UmVmZXJlbmNlTWFuaWZlc3QsXG4gICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zTWFuaWZlc3QsXG4gICAgICAgICAgICBzZXJ2ZXJNb2R1bGVNYXA6IGNyZWF0ZVNlcnZlck1vZHVsZU1hcCh7XG4gICAgICAgICAgICAgICAgc2VydmVyQWN0aW9uc01hbmlmZXN0XG4gICAgICAgICAgICB9KVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgY29uc3QgbWV0aG9kID0gcmVxLm1ldGhvZCB8fCAnR0VUJztcbiAgICBjb25zdCB0cmFjZXIgPSBnZXRUcmFjZXIoKTtcbiAgICBjb25zdCBhY3RpdmVTcGFuID0gdHJhY2VyLmdldEFjdGl2ZVNjb3BlU3BhbigpO1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGludm9rZVJvdXRlTW9kdWxlID0gYXN5bmMgKHNwYW4sIGNvbnRleHQpPT57XG4gICAgICAgICAgICBjb25zdCBuZXh0UmVxID0gbmV3IE5vZGVOZXh0UmVxdWVzdChyZXEpO1xuICAgICAgICAgICAgY29uc3QgbmV4dFJlcyA9IG5ldyBOb2RlTmV4dFJlc3BvbnNlKHJlcyk7XG4gICAgICAgICAgICAvLyBUT0RPOiBhZGFwdCBmb3IgcHV0dGluZyB0aGUgUkRDIGluc2lkZSB0aGUgcG9zdHBvbmVkIGRhdGFcbiAgICAgICAgICAgIC8vIElmIHdlJ3JlIGluIGRldiwgYW5kIHRoaXMgaXNuJ3QgYSBwcmVmZXRjaCBvciBhIHNlcnZlciBhY3Rpb24sXG4gICAgICAgICAgICAvLyB3ZSBzaG91bGQgc2VlZCB0aGUgcmVzdW1lIGRhdGEgY2FjaGUuXG4gICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgICAgICAgICAgICBpZiAobmV4dENvbmZpZy5leHBlcmltZW50YWwuZHluYW1pY0lPICYmICFpc1ByZWZldGNoUlNDUmVxdWVzdCAmJiAhY29udGV4dC5yZW5kZXJPcHRzLmlzUG9zc2libGVTZXJ2ZXJBY3Rpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgd2FybXVwID0gYXdhaXQgcm91dGVNb2R1bGUud2FybXVwKG5leHRSZXEsIG5leHRSZXMsIGNvbnRleHQpO1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiB0aGUgd2FybXVwIGlzIHN1Y2Nlc3NmdWwsIHdlIHNob3VsZCB1c2UgdGhlIHJlc3VtZSBkYXRhXG4gICAgICAgICAgICAgICAgICAgIC8vIGNhY2hlIGZyb20gdGhlIHdhcm11cC5cbiAgICAgICAgICAgICAgICAgICAgaWYgKHdhcm11cC5tZXRhZGF0YS5yZW5kZXJSZXN1bWVEYXRhQ2FjaGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQucmVuZGVyT3B0cy5yZW5kZXJSZXN1bWVEYXRhQ2FjaGUgPSB3YXJtdXAubWV0YWRhdGEucmVuZGVyUmVzdW1lRGF0YUNhY2hlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJvdXRlTW9kdWxlLnJlbmRlcihuZXh0UmVxLCBuZXh0UmVzLCBjb250ZXh0KS5maW5hbGx5KCgpPT57XG4gICAgICAgICAgICAgICAgaWYgKCFzcGFuKSByZXR1cm47XG4gICAgICAgICAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGVzKHtcbiAgICAgICAgICAgICAgICAgICAgJ2h0dHAuc3RhdHVzX2NvZGUnOiByZXMuc3RhdHVzQ29kZSxcbiAgICAgICAgICAgICAgICAgICAgJ25leHQucnNjJzogZmFsc2VcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCByb290U3BhbkF0dHJpYnV0ZXMgPSB0cmFjZXIuZ2V0Um9vdFNwYW5BdHRyaWJ1dGVzKCk7XG4gICAgICAgICAgICAgICAgLy8gV2Ugd2VyZSB1bmFibGUgdG8gZ2V0IGF0dHJpYnV0ZXMsIHByb2JhYmx5IE9URUwgaXMgbm90IGVuYWJsZWRcbiAgICAgICAgICAgICAgICBpZiAoIXJvb3RTcGFuQXR0cmlidXRlcykge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChyb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnNwYW5fdHlwZScpICE9PSBCYXNlU2VydmVyU3Bhbi5oYW5kbGVSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgVW5leHBlY3RlZCByb290IHNwYW4gdHlwZSAnJHtyb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnNwYW5fdHlwZScpfScuIFBsZWFzZSByZXBvcnQgdGhpcyBOZXh0LmpzIGlzc3VlIGh0dHBzOi8vZ2l0aHViLmNvbS92ZXJjZWwvbmV4dC5qc2ApO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHJvdXRlID0gcm9vdFNwYW5BdHRyaWJ1dGVzLmdldCgnbmV4dC5yb3V0ZScpO1xuICAgICAgICAgICAgICAgIGlmIChyb3V0ZSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBuYW1lID0gYCR7bWV0aG9kfSAke3JvdXRlfWA7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlcyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAnbmV4dC5yb3V0ZSc6IHJvdXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ2h0dHAucm91dGUnOiByb3V0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICduZXh0LnNwYW5fbmFtZSc6IG5hbWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4udXBkYXRlTmFtZShuYW1lKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnVwZGF0ZU5hbWUoYCR7bWV0aG9kfSAke3JlcS51cmx9YCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGRvUmVuZGVyID0gYXN5bmMgKHsgc3BhbiwgcG9zdHBvbmVkLCBmYWxsYmFja1JvdXRlUGFyYW1zIH0pPT57XG4gICAgICAgICAgICBjb25zdCBjb250ZXh0ID0ge1xuICAgICAgICAgICAgICAgIHF1ZXJ5LFxuICAgICAgICAgICAgICAgIHBhcmFtcyxcbiAgICAgICAgICAgICAgICBwYWdlOiBub3JtYWxpemVkU3JjUGFnZSxcbiAgICAgICAgICAgICAgICBzaGFyZWRDb250ZXh0OiB7XG4gICAgICAgICAgICAgICAgICAgIGJ1aWxkSWRcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHNlcnZlckNvbXBvbmVudHNIbXJDYWNoZTogZ2V0UmVxdWVzdE1ldGEocmVxLCAnc2VydmVyQ29tcG9uZW50c0htckNhY2hlJyksXG4gICAgICAgICAgICAgICAgZmFsbGJhY2tSb3V0ZVBhcmFtcyxcbiAgICAgICAgICAgICAgICByZW5kZXJPcHRzOiB7XG4gICAgICAgICAgICAgICAgICAgIEFwcDogKCk9Pm51bGwsXG4gICAgICAgICAgICAgICAgICAgIERvY3VtZW50OiAoKT0+bnVsbCxcbiAgICAgICAgICAgICAgICAgICAgcGFnZUNvbmZpZzoge30sXG4gICAgICAgICAgICAgICAgICAgIENvbXBvbmVudE1vZCxcbiAgICAgICAgICAgICAgICAgICAgQ29tcG9uZW50OiBpbnRlcm9wRGVmYXVsdChDb21wb25lbnRNb2QpLFxuICAgICAgICAgICAgICAgICAgICBwYXJhbXMsXG4gICAgICAgICAgICAgICAgICAgIHJvdXRlTW9kdWxlLFxuICAgICAgICAgICAgICAgICAgICBwYWdlOiBzcmNQYWdlLFxuICAgICAgICAgICAgICAgICAgICBwb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgICAgIHNob3VsZFdhaXRPbkFsbFJlYWR5LFxuICAgICAgICAgICAgICAgICAgICBzZXJ2ZVN0cmVhbWluZ01ldGFkYXRhLFxuICAgICAgICAgICAgICAgICAgICBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZTogdHlwZW9mIHBvc3Rwb25lZCA9PT0gJ3N0cmluZycgfHwgc3VwcG9ydHNEeW5hbWljUmVzcG9uc2UsXG4gICAgICAgICAgICAgICAgICAgIGJ1aWxkTWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgIG5leHRGb250TWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgIHJlYWN0TG9hZGFibGVNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgc3VicmVzb3VyY2VJbnRlZ3JpdHlNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgc2VydmVyQWN0aW9uc01hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBjbGllbnRSZWZlcmVuY2VNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgc2V0SXNyU3RhdHVzOiByb3V0ZXJTZXJ2ZXJDb250ZXh0ID09IG51bGwgPyB2b2lkIDAgOiByb3V0ZXJTZXJ2ZXJDb250ZXh0LnNldElzclN0YXR1cyxcbiAgICAgICAgICAgICAgICAgICAgZGlyOiByb3V0ZU1vZHVsZS5wcm9qZWN0RGlyLFxuICAgICAgICAgICAgICAgICAgICBpc0RyYWZ0TW9kZSxcbiAgICAgICAgICAgICAgICAgICAgaXNSZXZhbGlkYXRlOiBpc1NTRyAmJiAhcG9zdHBvbmVkICYmICFpc0R5bmFtaWNSU0NSZXF1ZXN0LFxuICAgICAgICAgICAgICAgICAgICBib3RUeXBlLFxuICAgICAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgaXNQb3NzaWJsZVNlcnZlckFjdGlvbixcbiAgICAgICAgICAgICAgICAgICAgYXNzZXRQcmVmaXg6IG5leHRDb25maWcuYXNzZXRQcmVmaXgsXG4gICAgICAgICAgICAgICAgICAgIG5leHRDb25maWdPdXRwdXQ6IG5leHRDb25maWcub3V0cHV0LFxuICAgICAgICAgICAgICAgICAgICBjcm9zc09yaWdpbjogbmV4dENvbmZpZy5jcm9zc09yaWdpbixcbiAgICAgICAgICAgICAgICAgICAgdHJhaWxpbmdTbGFzaDogbmV4dENvbmZpZy50cmFpbGluZ1NsYXNoLFxuICAgICAgICAgICAgICAgICAgICBwcmV2aWV3UHJvcHM6IHByZXJlbmRlck1hbmlmZXN0LnByZXZpZXcsXG4gICAgICAgICAgICAgICAgICAgIGRlcGxveW1lbnRJZDogbmV4dENvbmZpZy5kZXBsb3ltZW50SWQsXG4gICAgICAgICAgICAgICAgICAgIGVuYWJsZVRhaW50aW5nOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC50YWludCxcbiAgICAgICAgICAgICAgICAgICAgaHRtbExpbWl0ZWRCb3RzOiBuZXh0Q29uZmlnLmh0bWxMaW1pdGVkQm90cyxcbiAgICAgICAgICAgICAgICAgICAgZGV2dG9vbFNlZ21lbnRFeHBsb3JlcjogbmV4dENvbmZpZy5leHBlcmltZW50YWwuZGV2dG9vbFNlZ21lbnRFeHBsb3JlcixcbiAgICAgICAgICAgICAgICAgICAgcmVhY3RNYXhIZWFkZXJzTGVuZ3RoOiBuZXh0Q29uZmlnLnJlYWN0TWF4SGVhZGVyc0xlbmd0aCxcbiAgICAgICAgICAgICAgICAgICAgbXVsdGlab25lRHJhZnRNb2RlLFxuICAgICAgICAgICAgICAgICAgICBpbmNyZW1lbnRhbENhY2hlOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdpbmNyZW1lbnRhbENhY2hlJyksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlTGlmZVByb2ZpbGVzOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5jYWNoZUxpZmUsXG4gICAgICAgICAgICAgICAgICAgIGJhc2VQYXRoOiBuZXh0Q29uZmlnLmJhc2VQYXRoLFxuICAgICAgICAgICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5zZXJ2ZXJBY3Rpb25zLFxuICAgICAgICAgICAgICAgICAgICAuLi5pc0RlYnVnU3RhdGljU2hlbGwgfHwgaXNEZWJ1Z0R5bmFtaWNBY2Nlc3NlcyA/IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5leHRFeHBvcnQ6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1N0YXRpY0dlbmVyYXRpb246IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1JldmFsaWRhdGU6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzOiBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzXG4gICAgICAgICAgICAgICAgICAgIH0gOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgZXhwZXJpbWVudGFsOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpc1JvdXRlUFBSRW5hYmxlZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZVRpbWU6IG5leHRDb25maWcuZXhwaXJlVGltZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YWxlVGltZXM6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLnN0YWxlVGltZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICBkeW5hbWljSU86IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuZHluYW1pY0lPKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsaWVudFNlZ21lbnRDYWNoZTogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5jbGllbnRTZWdtZW50Q2FjaGUpLFxuICAgICAgICAgICAgICAgICAgICAgICAgZHluYW1pY09uSG92ZXI6IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuZHluYW1pY09uSG92ZXIpLFxuICAgICAgICAgICAgICAgICAgICAgICAgaW5saW5lQ3NzOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmlubGluZUNzcyksXG4gICAgICAgICAgICAgICAgICAgICAgICBhdXRoSW50ZXJydXB0czogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5hdXRoSW50ZXJydXB0cyksXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGllbnRUcmFjZU1ldGFkYXRhOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5jbGllbnRUcmFjZU1ldGFkYXRhIHx8IFtdXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHdhaXRVbnRpbDogY3R4LndhaXRVbnRpbCxcbiAgICAgICAgICAgICAgICAgICAgb25DbG9zZTogKGNiKT0+e1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLm9uKCdjbG9zZScsIGNiKTtcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgb25BZnRlclRhc2tFcnJvcjogKCk9Pnt9LFxuICAgICAgICAgICAgICAgICAgICBvbkluc3RydW1lbnRhdGlvblJlcXVlc3RFcnJvcjogKGVycm9yLCBfcmVxdWVzdCwgZXJyb3JDb250ZXh0KT0+cm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnJvciwgZXJyb3JDb250ZXh0LCByb3V0ZXJTZXJ2ZXJDb250ZXh0KSxcbiAgICAgICAgICAgICAgICAgICAgZXJyOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdpbnZva2VFcnJvcicpLFxuICAgICAgICAgICAgICAgICAgICBkZXY6IHJvdXRlTW9kdWxlLmlzRGV2XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGludm9rZVJvdXRlTW9kdWxlKHNwYW4sIGNvbnRleHQpO1xuICAgICAgICAgICAgY29uc3QgeyBtZXRhZGF0YSB9ID0gcmVzdWx0O1xuICAgICAgICAgICAgY29uc3QgeyBjYWNoZUNvbnRyb2wsIGhlYWRlcnMgPSB7fSwgLy8gQWRkIGFueSBmZXRjaCB0YWdzIHRoYXQgd2VyZSBvbiB0aGUgcGFnZSB0byB0aGUgcmVzcG9uc2UgaGVhZGVycy5cbiAgICAgICAgICAgIGZldGNoVGFnczogY2FjaGVUYWdzIH0gPSBtZXRhZGF0YTtcbiAgICAgICAgICAgIGlmIChjYWNoZVRhZ3MpIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzW05FWFRfQ0FDSEVfVEFHU19IRUFERVJdID0gY2FjaGVUYWdzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gUHVsbCBhbnkgZmV0Y2ggbWV0cmljcyBmcm9tIHRoZSByZW5kZXIgb250byB0aGUgcmVxdWVzdC5cbiAgICAgICAgICAgIDtcbiAgICAgICAgICAgIHJlcS5mZXRjaE1ldHJpY3MgPSBtZXRhZGF0YS5mZXRjaE1ldHJpY3M7XG4gICAgICAgICAgICAvLyB3ZSBkb24ndCB0aHJvdyBzdGF0aWMgdG8gZHluYW1pYyBlcnJvcnMgaW4gZGV2IGFzIGlzU1NHXG4gICAgICAgICAgICAvLyBpcyBhIGJlc3QgZ3Vlc3MgaW4gZGV2IHNpbmNlIHdlIGRvbid0IGhhdmUgdGhlIHByZXJlbmRlciBwYXNzXG4gICAgICAgICAgICAvLyB0byBrbm93IHdoZXRoZXIgdGhlIHBhdGggaXMgYWN0dWFsbHkgc3RhdGljIG9yIG5vdFxuICAgICAgICAgICAgaWYgKGlzU1NHICYmIChjYWNoZUNvbnRyb2wgPT0gbnVsbCA/IHZvaWQgMCA6IGNhY2hlQ29udHJvbC5yZXZhbGlkYXRlKSA9PT0gMCAmJiAhcm91dGVNb2R1bGUuaXNEZXYgJiYgIWlzUm91dGVQUFJFbmFibGVkKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3RhdGljQmFpbG91dEluZm8gPSBtZXRhZGF0YS5zdGF0aWNCYWlsb3V0SW5mbztcbiAgICAgICAgICAgICAgICBjb25zdCBlcnIgPSBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBQYWdlIGNoYW5nZWQgZnJvbSBzdGF0aWMgdG8gZHluYW1pYyBhdCBydW50aW1lICR7cmVzb2x2ZWRQYXRobmFtZX0keyhzdGF0aWNCYWlsb3V0SW5mbyA9PSBudWxsID8gdm9pZCAwIDogc3RhdGljQmFpbG91dEluZm8uZGVzY3JpcHRpb24pID8gYCwgcmVhc29uOiAke3N0YXRpY0JhaWxvdXRJbmZvLmRlc2NyaXB0aW9ufWAgOiBgYH1gICsgYFxcbnNlZSBtb3JlIGhlcmUgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvYXBwLXN0YXRpYy10by1keW5hbWljLWVycm9yYCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMTMyXCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpZiAoc3RhdGljQmFpbG91dEluZm8gPT0gbnVsbCA/IHZvaWQgMCA6IHN0YXRpY0JhaWxvdXRJbmZvLnN0YWNrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHN0YWNrID0gc3RhdGljQmFpbG91dEluZm8uc3RhY2s7XG4gICAgICAgICAgICAgICAgICAgIGVyci5zdGFjayA9IGVyci5tZXNzYWdlICsgc3RhY2suc3Vic3RyaW5nKHN0YWNrLmluZGV4T2YoJ1xcbicpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICB2YWx1ZToge1xuICAgICAgICAgICAgICAgICAgICBraW5kOiBDYWNoZWRSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgICAgICAgICAgICAgIGh0bWw6IHJlc3VsdCxcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVycyxcbiAgICAgICAgICAgICAgICAgICAgcnNjRGF0YTogbWV0YWRhdGEuZmxpZ2h0RGF0YSxcbiAgICAgICAgICAgICAgICAgICAgcG9zdHBvbmVkOiBtZXRhZGF0YS5wb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogbWV0YWRhdGEuc3RhdHVzQ29kZSxcbiAgICAgICAgICAgICAgICAgICAgc2VnbWVudERhdGE6IG1ldGFkYXRhLnNlZ21lbnREYXRhXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2xcbiAgICAgICAgICAgIH07XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlR2VuZXJhdG9yID0gYXN5bmMgKHsgaGFzUmVzb2x2ZWQsIHByZXZpb3VzQ2FjaGVFbnRyeSwgaXNSZXZhbGlkYXRpbmcsIHNwYW4gfSk9PntcbiAgICAgICAgICAgIGNvbnN0IGlzUHJvZHVjdGlvbiA9IHJvdXRlTW9kdWxlLmlzRGV2ID09PSBmYWxzZTtcbiAgICAgICAgICAgIGNvbnN0IGRpZFJlc3BvbmQgPSBoYXNSZXNvbHZlZCB8fCByZXMud3JpdGFibGVFbmRlZDtcbiAgICAgICAgICAgIC8vIHNraXAgb24tZGVtYW5kIHJldmFsaWRhdGUgaWYgY2FjaGUgaXMgbm90IHByZXNlbnQgYW5kXG4gICAgICAgICAgICAvLyByZXZhbGlkYXRlLWlmLWdlbmVyYXRlZCBpcyBzZXRcbiAgICAgICAgICAgIGlmIChpc09uRGVtYW5kUmV2YWxpZGF0ZSAmJiByZXZhbGlkYXRlT25seUdlbmVyYXRlZCAmJiAhcHJldmlvdXNDYWNoZUVudHJ5ICYmICFtaW5pbWFsTW9kZSkge1xuICAgICAgICAgICAgICAgIGlmIChyb3V0ZXJTZXJ2ZXJDb250ZXh0ID09IG51bGwgPyB2b2lkIDAgOiByb3V0ZXJTZXJ2ZXJDb250ZXh0LnJlbmRlcjQwNCkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCByb3V0ZXJTZXJ2ZXJDb250ZXh0LnJlbmRlcjQwNChyZXEsIHJlcyk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSA0MDQ7XG4gICAgICAgICAgICAgICAgICAgIHJlcy5lbmQoJ1RoaXMgcGFnZSBjb3VsZCBub3QgYmUgZm91bmQnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgZmFsbGJhY2tNb2RlO1xuICAgICAgICAgICAgaWYgKHByZXJlbmRlckluZm8pIHtcbiAgICAgICAgICAgICAgICBmYWxsYmFja01vZGUgPSBwYXJzZUZhbGxiYWNrRmllbGQocHJlcmVuZGVySW5mby5mYWxsYmFjayk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBXaGVuIHNlcnZpbmcgYSBib3QgcmVxdWVzdCwgd2Ugd2FudCB0byBzZXJ2ZSBhIGJsb2NraW5nIHJlbmRlciBhbmQgbm90XG4gICAgICAgICAgICAvLyB0aGUgcHJlcmVuZGVyZWQgcGFnZS4gVGhpcyBlbnN1cmVzIHRoYXQgdGhlIGNvcnJlY3QgY29udGVudCBpcyBzZXJ2ZWRcbiAgICAgICAgICAgIC8vIHRvIHRoZSBib3QgaW4gdGhlIGhlYWQuXG4gICAgICAgICAgICBpZiAoZmFsbGJhY2tNb2RlID09PSBGYWxsYmFja01vZGUuUFJFUkVOREVSICYmIGlzQm90KHVzZXJBZ2VudCkpIHtcbiAgICAgICAgICAgICAgICBmYWxsYmFja01vZGUgPSBGYWxsYmFja01vZGUuQkxPQ0tJTkdfU1RBVElDX1JFTkRFUjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICgocHJldmlvdXNDYWNoZUVudHJ5ID09IG51bGwgPyB2b2lkIDAgOiBwcmV2aW91c0NhY2hlRW50cnkuaXNTdGFsZSkgPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgaXNPbkRlbWFuZFJldmFsaWRhdGUgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gVE9ETzogYWRhcHQgZm9yIFBQUlxuICAgICAgICAgICAgLy8gb25seSBhbGxvdyBvbi1kZW1hbmQgcmV2YWxpZGF0ZSBmb3IgZmFsbGJhY2s6IHRydWUvYmxvY2tpbmdcbiAgICAgICAgICAgIC8vIG9yIGZvciBwcmVyZW5kZXJlZCBmYWxsYmFjazogZmFsc2UgcGF0aHNcbiAgICAgICAgICAgIGlmIChpc09uRGVtYW5kUmV2YWxpZGF0ZSAmJiAoZmFsbGJhY2tNb2RlICE9PSBGYWxsYmFja01vZGUuTk9UX0ZPVU5EIHx8IHByZXZpb3VzQ2FjaGVFbnRyeSkpIHtcbiAgICAgICAgICAgICAgICBmYWxsYmFja01vZGUgPSBGYWxsYmFja01vZGUuQkxPQ0tJTkdfU1RBVElDX1JFTkRFUjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUgJiYgZmFsbGJhY2tNb2RlICE9PSBGYWxsYmFja01vZGUuQkxPQ0tJTkdfU1RBVElDX1JFTkRFUiAmJiBzdGF0aWNQYXRoS2V5ICYmICFkaWRSZXNwb25kICYmICFpc0RyYWZ0TW9kZSAmJiBwYWdlSXNEeW5hbWljICYmIChpc1Byb2R1Y3Rpb24gfHwgIWlzUHJlcmVuZGVyZWQpKSB7XG4gICAgICAgICAgICAgICAgLy8gaWYgdGhlIHBhZ2UgaGFzIGR5bmFtaWNQYXJhbXM6IGZhbHNlIGFuZCB0aGlzIHBhdGhuYW1lIHdhc24ndFxuICAgICAgICAgICAgICAgIC8vIHByZXJlbmRlcmVkIHRyaWdnZXIgdGhlIG5vIGZhbGxiYWNrIGhhbmRsaW5nXG4gICAgICAgICAgICAgICAgaWYgKC8vIEluIGRldmVsb3BtZW50LCBmYWxsIHRocm91Z2ggdG8gcmVuZGVyIHRvIGhhbmRsZSBtaXNzaW5nXG4gICAgICAgICAgICAgICAgLy8gZ2V0U3RhdGljUGF0aHMuXG4gICAgICAgICAgICAgICAgKGlzUHJvZHVjdGlvbiB8fCBwcmVyZW5kZXJJbmZvKSAmJiAvLyBXaGVuIGZhbGxiYWNrIGlzbid0IHByZXNlbnQsIGFib3J0IHRoaXMgcmVuZGVyIHNvIHdlIDQwNFxuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9PT0gRmFsbGJhY2tNb2RlLk5PVF9GT1VORCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgTm9GYWxsYmFja0Vycm9yKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxldCBmYWxsYmFja1Jlc3BvbnNlO1xuICAgICAgICAgICAgICAgIGlmIChpc1JvdXRlUFBSRW5hYmxlZCAmJiAhaXNSU0NSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFdlIHVzZSB0aGUgcmVzcG9uc2UgY2FjaGUgaGVyZSB0byBoYW5kbGUgdGhlIHJldmFsaWRhdGlvbiBhbmRcbiAgICAgICAgICAgICAgICAgICAgLy8gbWFuYWdlbWVudCBvZiB0aGUgZmFsbGJhY2sgc2hlbGwuXG4gICAgICAgICAgICAgICAgICAgIGZhbGxiYWNrUmVzcG9uc2UgPSBhd2FpdCByb3V0ZU1vZHVsZS5oYW5kbGVSZXNwb25zZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUtleTogaXNQcm9kdWN0aW9uID8gbm9ybWFsaXplZFNyY1BhZ2UgOiBudWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICAgICAgbmV4dENvbmZpZyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlS2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICAgICAgICAgICAgICAgICAgaXNGYWxsYmFjazogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHByZXJlbmRlck1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICAgICAgaXNSb3V0ZVBQUkVuYWJsZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zZUdlbmVyYXRvcjogYXN5bmMgKCk9PmRvUmVuZGVyKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gV2UgcGFzcyBgdW5kZWZpbmVkYCBhcyByZW5kZXJpbmcgYSBmYWxsYmFjayBpc24ndCByZXN1bWVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGhlcmUuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvc3Rwb25lZDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zOiAvLyBJZiB3ZSdyZSBpbiBwcm9kdWN0aW9uIG9yIHdlJ3JlIGRlYnVnZ2luZyB0aGUgZmFsbGJhY2tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gc2hlbGwgdGhlbiB3ZSBzaG91bGQgcG9zdHBvbmUgd2hlbiBkeW5hbWljIHBhcmFtcyBhcmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gYWNjZXNzZWQuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUHJvZHVjdGlvbiB8fCBpc0RlYnVnRmFsbGJhY2tTaGVsbCA/IGdldEZhbGxiYWNrUm91dGVQYXJhbXMobm9ybWFsaXplZFNyY1BhZ2UpIDogbnVsbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgICAgICAgICAgd2FpdFVudGlsOiBjdHgud2FpdFVudGlsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiB0aGUgZmFsbGJhY2sgcmVzcG9uc2Ugd2FzIHNldCB0byBudWxsLCB0aGVuIHdlIHNob3VsZCByZXR1cm4gbnVsbC5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGZhbGxiYWNrUmVzcG9uc2UgPT09IG51bGwpIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgICAgICAvLyBPdGhlcndpc2UsIGlmIHdlIGRpZCBnZXQgYSBmYWxsYmFjayByZXNwb25zZSwgd2Ugc2hvdWxkIHJldHVybiBpdC5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGZhbGxiYWNrUmVzcG9uc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFJlbW92ZSB0aGUgY2FjaGUgY29udHJvbCBmcm9tIHRoZSByZXNwb25zZSB0byBwcmV2ZW50IGl0IGZyb20gYmVpbmdcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIHVzZWQgaW4gdGhlIHN1cnJvdW5kaW5nIGNhY2hlLlxuICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIGZhbGxiYWNrUmVzcG9uc2UuY2FjaGVDb250cm9sO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbGxiYWNrUmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBPbmx5IHJlcXVlc3RzIHRoYXQgYXJlbid0IHJldmFsaWRhdGluZyBjYW4gYmUgcmVzdW1lZC4gSWYgd2UgaGF2ZSB0aGVcbiAgICAgICAgICAgIC8vIG1pbmltYWwgcG9zdHBvbmVkIGRhdGEsIHRoZW4gd2Ugc2hvdWxkIHJlc3VtZSB0aGUgcmVuZGVyIHdpdGggaXQuXG4gICAgICAgICAgICBjb25zdCBwb3N0cG9uZWQgPSAhaXNPbkRlbWFuZFJldmFsaWRhdGUgJiYgIWlzUmV2YWxpZGF0aW5nICYmIG1pbmltYWxQb3N0cG9uZWQgPyBtaW5pbWFsUG9zdHBvbmVkIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgLy8gV2hlbiB3ZSdyZSBpbiBtaW5pbWFsIG1vZGUsIGlmIHdlJ3JlIHRyeWluZyB0byBkZWJ1ZyB0aGUgc3RhdGljIHNoZWxsLFxuICAgICAgICAgICAgLy8gd2Ugc2hvdWxkIGp1c3QgcmV0dXJuIG5vdGhpbmcgaW5zdGVhZCBvZiByZXN1bWluZyB0aGUgZHluYW1pYyByZW5kZXIuXG4gICAgICAgICAgICBpZiAoKGlzRGVidWdTdGF0aWNTaGVsbCB8fCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzKSAmJiB0eXBlb2YgcG9zdHBvbmVkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBraW5kOiBDYWNoZWRSb3V0ZUtpbmQuUEFHRVMsXG4gICAgICAgICAgICAgICAgICAgICAgICBodG1sOiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYygnJyksXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWdlRGF0YToge30sXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSBkeW5hbWljIHJvdXRlIHdpdGggUFBSIGVuYWJsZWQgYW5kIHRoZSBkZWZhdWx0IHJvdXRlXG4gICAgICAgICAgICAvLyBtYXRjaGVzIHdlcmUgc2V0LCB0aGVuIHdlIHNob3VsZCBwYXNzIHRoZSBmYWxsYmFjayByb3V0ZSBwYXJhbXMgdG9cbiAgICAgICAgICAgIC8vIHRoZSByZW5kZXJlciBhcyB0aGlzIGlzIGEgZmFsbGJhY2sgcmV2YWxpZGF0aW9uIHJlcXVlc3QuXG4gICAgICAgICAgICBjb25zdCBmYWxsYmFja1JvdXRlUGFyYW1zID0gcGFnZUlzRHluYW1pYyAmJiBpc1JvdXRlUFBSRW5hYmxlZCAmJiAoZ2V0UmVxdWVzdE1ldGEocmVxLCAncmVuZGVyRmFsbGJhY2tTaGVsbCcpIHx8IGlzRGVidWdGYWxsYmFja1NoZWxsKSA/IGdldEZhbGxiYWNrUm91dGVQYXJhbXMocGF0aG5hbWUpIDogbnVsbDtcbiAgICAgICAgICAgIC8vIFBlcmZvcm0gdGhlIHJlbmRlci5cbiAgICAgICAgICAgIHJldHVybiBkb1JlbmRlcih7XG4gICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICBwb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgZmFsbGJhY2tSb3V0ZVBhcmFtc1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGhhbmRsZVJlc3BvbnNlID0gYXN5bmMgKHNwYW4pPT57XG4gICAgICAgICAgICB2YXIgX2NhY2hlRW50cnlfdmFsdWUsIF9jYWNoZWREYXRhX2hlYWRlcnM7XG4gICAgICAgICAgICBjb25zdCBjYWNoZUVudHJ5ID0gYXdhaXQgcm91dGVNb2R1bGUuaGFuZGxlUmVzcG9uc2Uoe1xuICAgICAgICAgICAgICAgIGNhY2hlS2V5OiBzc2dDYWNoZUtleSxcbiAgICAgICAgICAgICAgICByZXNwb25zZUdlbmVyYXRvcjogKGMpPT5yZXNwb25zZUdlbmVyYXRvcih7XG4gICAgICAgICAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uY1xuICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICByb3V0ZUtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICBpc1JvdXRlUFBSRW5hYmxlZCxcbiAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgbmV4dENvbmZpZyxcbiAgICAgICAgICAgICAgICBwcmVyZW5kZXJNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICB3YWl0VW50aWw6IGN0eC53YWl0VW50aWxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKGlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcignQ2FjaGUtQ29udHJvbCcsICdwcml2YXRlLCBuby1jYWNoZSwgbm8tc3RvcmUsIG1heC1hZ2U9MCwgbXVzdC1yZXZhbGlkYXRlJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJbiBkZXYsIHdlIHNob3VsZCBub3QgY2FjaGUgcGFnZXMgZm9yIGFueSByZWFzb24uXG4gICAgICAgICAgICBpZiAocm91dGVNb2R1bGUuaXNEZXYpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCdDYWNoZS1Db250cm9sJywgJ25vLXN0b3JlLCBtdXN0LXJldmFsaWRhdGUnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghY2FjaGVFbnRyeSkge1xuICAgICAgICAgICAgICAgIGlmIChzc2dDYWNoZUtleSkge1xuICAgICAgICAgICAgICAgICAgICAvLyBBIGNhY2hlIGVudHJ5IG1pZ2h0IG5vdCBiZSBnZW5lcmF0ZWQgaWYgYSByZXNwb25zZSBpcyB3cml0dGVuXG4gICAgICAgICAgICAgICAgICAgIC8vIGluIGBnZXRJbml0aWFsUHJvcHNgIG9yIGBnZXRTZXJ2ZXJTaWRlUHJvcHNgLCBidXQgdGhvc2Ugc2hvdWxkbid0XG4gICAgICAgICAgICAgICAgICAgIC8vIGhhdmUgYSBjYWNoZSBrZXkuIElmIHdlIGRvIGhhdmUgYSBjYWNoZSBrZXkgYnV0IHdlIGRvbid0IGVuZCB1cFxuICAgICAgICAgICAgICAgICAgICAvLyB3aXRoIGEgY2FjaGUgZW50cnksIHRoZW4gZWl0aGVyIE5leHQuanMgb3IgdGhlIGFwcGxpY2F0aW9uIGhhcyBhXG4gICAgICAgICAgICAgICAgICAgIC8vIGJ1ZyB0aGF0IG5lZWRzIGZpeGluZy5cbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignaW52YXJpYW50OiBjYWNoZSBlbnRyeSByZXF1aXJlZCBidXQgbm90IGdlbmVyYXRlZCcpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU2MlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCgoX2NhY2hlRW50cnlfdmFsdWUgPSBjYWNoZUVudHJ5LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfdmFsdWUua2luZCkgIT09IENhY2hlZFJvdXRlS2luZC5BUFBfUEFHRSkge1xuICAgICAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV92YWx1ZTE7XG4gICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihgSW52YXJpYW50IGFwcC1wYWdlIGhhbmRsZXIgcmVjZWl2ZWQgaW52YWxpZCBjYWNoZSBlbnRyeSAkeyhfY2FjaGVFbnRyeV92YWx1ZTEgPSBjYWNoZUVudHJ5LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfdmFsdWUxLmtpbmR9YCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFNzA3XCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGRpZFBvc3Rwb25lID0gdHlwZW9mIGNhY2hlRW50cnkudmFsdWUucG9zdHBvbmVkID09PSAnc3RyaW5nJztcbiAgICAgICAgICAgIGlmIChpc1NTRyAmJiAvLyBXZSBkb24ndCB3YW50IHRvIHNlbmQgYSBjYWNoZSBoZWFkZXIgZm9yIHJlcXVlc3RzIHRoYXQgY29udGFpbiBkeW5hbWljXG4gICAgICAgICAgICAvLyBkYXRhLiBJZiB0aGlzIGlzIGEgRHluYW1pYyBSU0MgcmVxdWVzdCBvciB3YXNuJ3QgYSBQcmVmZXRjaCBSU0NcbiAgICAgICAgICAgIC8vIHJlcXVlc3QsIHRoZW4gd2Ugc2hvdWxkIHNldCB0aGUgY2FjaGUgaGVhZGVyLlxuICAgICAgICAgICAgIWlzRHluYW1pY1JTQ1JlcXVlc3QgJiYgKCFkaWRQb3N0cG9uZSB8fCBpc1ByZWZldGNoUlNDUmVxdWVzdCkpIHtcbiAgICAgICAgICAgICAgICBpZiAoIW1pbmltYWxNb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIHNldCB4LW5leHRqcy1jYWNoZSBoZWFkZXIgdG8gbWF0Y2ggdGhlIGhlYWRlclxuICAgICAgICAgICAgICAgICAgICAvLyB3ZSBzZXQgZm9yIHRoZSBpbWFnZS1vcHRpbWl6ZXJcbiAgICAgICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcigneC1uZXh0anMtY2FjaGUnLCBpc09uRGVtYW5kUmV2YWxpZGF0ZSA/ICdSRVZBTElEQVRFRCcgOiBjYWNoZUVudHJ5LmlzTWlzcyA/ICdNSVNTJyA6IGNhY2hlRW50cnkuaXNTdGFsZSA/ICdTVEFMRScgOiAnSElUJyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFNldCBhIGhlYWRlciB1c2VkIGJ5IHRoZSBjbGllbnQgcm91dGVyIHRvIHNpZ25hbCB0aGUgcmVzcG9uc2UgaXMgc3RhdGljXG4gICAgICAgICAgICAgICAgLy8gYW5kIHNob3VsZCByZXNwZWN0IHRoZSBgc3RhdGljYCBjYWNoZSBzdGFsZVRpbWUgdmFsdWUuXG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0lTX1BSRVJFTkRFUl9IRUFERVIsICcxJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB7IHZhbHVlOiBjYWNoZWREYXRhIH0gPSBjYWNoZUVudHJ5O1xuICAgICAgICAgICAgLy8gQ29lcmNlIHRoZSBjYWNoZSBjb250cm9sIHBhcmFtZXRlciBmcm9tIHRoZSByZW5kZXIuXG4gICAgICAgICAgICBsZXQgY2FjaGVDb250cm9sO1xuICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIHJlc3VtZSByZXF1ZXN0IGluIG1pbmltYWwgbW9kZSBpdCBpcyBzdHJlYW1lZCB3aXRoIGR5bmFtaWNcbiAgICAgICAgICAgIC8vIGNvbnRlbnQgYW5kIHNob3VsZCBub3QgYmUgY2FjaGVkLlxuICAgICAgICAgICAgaWYgKG1pbmltYWxQb3N0cG9uZWQpIHtcbiAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobWluaW1hbE1vZGUgJiYgaXNSU0NSZXF1ZXN0ICYmICFpc1ByZWZldGNoUlNDUmVxdWVzdCAmJiBpc1JvdXRlUFBSRW5hYmxlZCkge1xuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbCA9IHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSBlbHNlIGlmICghcm91dGVNb2R1bGUuaXNEZXYpIHtcbiAgICAgICAgICAgICAgICAvLyBJZiB0aGlzIGlzIGEgcHJldmlldyBtb2RlIHJlcXVlc3QsIHdlIHNob3VsZG4ndCBjYWNoZSBpdFxuICAgICAgICAgICAgICAgIGlmIChpc0RyYWZ0TW9kZSkge1xuICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCFpc1NTRykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoIXJlcy5nZXRIZWFkZXIoJ0NhY2hlLUNvbnRyb2wnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGNhY2hlRW50cnkuY2FjaGVDb250cm9sKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSBjYWNoZSBlbnRyeSBoYXMgYSBjYWNoZSBjb250cm9sIHdpdGggYSByZXZhbGlkYXRlIHZhbHVlIHRoYXQnc1xuICAgICAgICAgICAgICAgICAgICAvLyBhIG51bWJlciwgdXNlIGl0LlxuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGNhY2hlRW50cnkuY2FjaGVDb250cm9sLnJldmFsaWRhdGUgPT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgX2NhY2hlRW50cnlfY2FjaGVDb250cm9sO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNhY2hlRW50cnkuY2FjaGVDb250cm9sLnJldmFsaWRhdGUgPCAxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihgSW52YWxpZCByZXZhbGlkYXRlIGNvbmZpZ3VyYXRpb24gcHJvdmlkZWQ6ICR7Y2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZX0gPCAxYCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMjJcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sLnJldmFsaWRhdGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiAoKF9jYWNoZUVudHJ5X2NhY2hlQ29udHJvbCA9IGNhY2hlRW50cnkuY2FjaGVDb250cm9sKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlRW50cnlfY2FjaGVDb250cm9sLmV4cGlyZSkgPz8gbmV4dENvbmZpZy5leHBpcmVUaW1lXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IENBQ0hFX09ORV9ZRUFSLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wgPSBjYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHNlZ21lbnRQcmVmZXRjaEhlYWRlciA9PT0gJ3N0cmluZycgJiYgKGNhY2hlZERhdGEgPT0gbnVsbCA/IHZvaWQgMCA6IGNhY2hlZERhdGEua2luZCkgPT09IENhY2hlZFJvdXRlS2luZC5BUFBfUEFHRSAmJiBjYWNoZWREYXRhLnNlZ21lbnREYXRhKSB7XG4gICAgICAgICAgICAgICAgdmFyIF9jYWNoZWREYXRhX2hlYWRlcnMxO1xuICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgYSBwcmVmZXRjaCByZXF1ZXN0IGlzc3VlZCBieSB0aGUgY2xpZW50IFNlZ21lbnQgQ2FjaGUuIFRoZXNlXG4gICAgICAgICAgICAgICAgLy8gc2hvdWxkIG5ldmVyIHJlYWNoIHRoZSBhcHBsaWNhdGlvbiBsYXllciAobGFtYmRhKS4gV2Ugc2hvdWxkIGVpdGhlclxuICAgICAgICAgICAgICAgIC8vIHJlc3BvbmQgZnJvbSB0aGUgY2FjaGUgKEhJVCkgb3IgcmVzcG9uZCB3aXRoIDIwNCBObyBDb250ZW50IChNSVNTKS5cbiAgICAgICAgICAgICAgICAvLyBTZXQgYSBoZWFkZXIgdG8gaW5kaWNhdGUgdGhhdCBQUFIgaXMgZW5hYmxlZCBmb3IgdGhpcyByb3V0ZS4gVGhpc1xuICAgICAgICAgICAgICAgIC8vIGxldHMgdGhlIGNsaWVudCBkaXN0aW5ndWlzaCBiZXR3ZWVuIGEgcmVndWxhciBjYWNoZSBtaXNzIGFuZCBhIGNhY2hlXG4gICAgICAgICAgICAgICAgLy8gbWlzcyBkdWUgdG8gUFBSIGJlaW5nIGRpc2FibGVkLiBJbiBvdGhlciBjb250ZXh0cyB0aGlzIGhlYWRlciBpcyB1c2VkXG4gICAgICAgICAgICAgICAgLy8gdG8gaW5kaWNhdGUgdGhhdCB0aGUgcmVzcG9uc2UgY29udGFpbnMgZHluYW1pYyBkYXRhLCBidXQgaGVyZSB3ZSdyZVxuICAgICAgICAgICAgICAgIC8vIG9ubHkgdXNpbmcgaXQgdG8gaW5kaWNhdGUgdGhhdCB0aGUgZmVhdHVyZSBpcyBlbmFibGVkIOKAlCB0aGUgc2VnbWVudFxuICAgICAgICAgICAgICAgIC8vIHJlc3BvbnNlIGl0c2VsZiBjb250YWlucyB3aGV0aGVyIHRoZSBkYXRhIGlzIGR5bmFtaWMuXG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIsICcyJyk7XG4gICAgICAgICAgICAgICAgLy8gQWRkIHRoZSBjYWNoZSB0YWdzIGhlYWRlciB0byB0aGUgcmVzcG9uc2UgaWYgaXQgZXhpc3RzIGFuZCB3ZSdyZSBpblxuICAgICAgICAgICAgICAgIC8vIG1pbmltYWwgbW9kZSB3aGlsZSByZW5kZXJpbmcgYSBzdGF0aWMgcGFnZS5cbiAgICAgICAgICAgICAgICBjb25zdCB0YWdzID0gKF9jYWNoZWREYXRhX2hlYWRlcnMxID0gY2FjaGVkRGF0YS5oZWFkZXJzKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlZERhdGFfaGVhZGVyczFbTkVYVF9DQUNIRV9UQUdTX0hFQURFUl07XG4gICAgICAgICAgICAgICAgaWYgKG1pbmltYWxNb2RlICYmIGlzU1NHICYmIHRhZ3MgJiYgdHlwZW9mIHRhZ3MgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoTkVYVF9DQUNIRV9UQUdTX0hFQURFUiwgdGFncyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IG1hdGNoZWRTZWdtZW50ID0gY2FjaGVkRGF0YS5zZWdtZW50RGF0YS5nZXQoc2VnbWVudFByZWZldGNoSGVhZGVyKTtcbiAgICAgICAgICAgICAgICBpZiAobWF0Y2hlZFNlZ21lbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBDYWNoZSBoaXRcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgICAgICBnZW5lcmF0ZUV0YWdzOiBuZXh0Q29uZmlnLmdlbmVyYXRlRXRhZ3MsXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYyhtYXRjaGVkU2VnbWVudCksXG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBDYWNoZSBtaXNzLiBFaXRoZXIgYSBjYWNoZSBlbnRyeSBmb3IgdGhpcyByb3V0ZSBoYXMgbm90IGJlZW4gZ2VuZXJhdGVkXG4gICAgICAgICAgICAgICAgLy8gKHdoaWNoIHRlY2huaWNhbGx5IHNob3VsZCBub3QgYmUgcG9zc2libGUgd2hlbiBQUFIgaXMgZW5hYmxlZCwgYmVjYXVzZVxuICAgICAgICAgICAgICAgIC8vIGF0IGEgbWluaW11bSB0aGVyZSBzaG91bGQgYWx3YXlzIGJlIGEgZmFsbGJhY2sgZW50cnkpIG9yIHRoZXJlJ3Mgbm9cbiAgICAgICAgICAgICAgICAvLyBtYXRjaCBmb3IgdGhlIHJlcXVlc3RlZCBzZWdtZW50LiBSZXNwb25kIHdpdGggYSAyMDQgTm8gQ29udGVudC4gV2VcbiAgICAgICAgICAgICAgICAvLyBkb24ndCBib3RoZXIgdG8gcmVzcG9uZCB3aXRoIDQwNCwgYmVjYXVzZSB0aGVzZSByZXF1ZXN0cyBhcmUgb25seVxuICAgICAgICAgICAgICAgIC8vIGlzc3VlZCBhcyBwYXJ0IG9mIGEgcHJlZmV0Y2guXG4gICAgICAgICAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSAyMDQ7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYygnJyksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZXJlJ3MgYSBjYWxsYmFjayBmb3IgYG9uQ2FjaGVFbnRyeWAsIGNhbGwgaXQgd2l0aCB0aGUgY2FjaGUgZW50cnlcbiAgICAgICAgICAgIC8vIGFuZCB0aGUgcmV2YWxpZGF0ZSBvcHRpb25zLlxuICAgICAgICAgICAgY29uc3Qgb25DYWNoZUVudHJ5ID0gZ2V0UmVxdWVzdE1ldGEocmVxLCAnb25DYWNoZUVudHJ5Jyk7XG4gICAgICAgICAgICBpZiAob25DYWNoZUVudHJ5KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmluaXNoZWQgPSBhd2FpdCBvbkNhY2hlRW50cnkoe1xuICAgICAgICAgICAgICAgICAgICAuLi5jYWNoZUVudHJ5LFxuICAgICAgICAgICAgICAgICAgICAvLyBUT0RPOiByZW1vdmUgdGhpcyB3aGVuIHVwc3RyZWFtIGRvZXNuJ3RcbiAgICAgICAgICAgICAgICAgICAgLy8gYWx3YXlzIGV4cGVjdCB0aGlzIHZhbHVlIHRvIGJlIFwiUEFHRVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5jYWNoZUVudHJ5LnZhbHVlLFxuICAgICAgICAgICAgICAgICAgICAgICAga2luZDogJ1BBR0UnXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LCB7XG4gICAgICAgICAgICAgICAgICAgIHVybDogZ2V0UmVxdWVzdE1ldGEocmVxLCAnaW5pdFVSTCcpXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgaWYgKGZpbmlzaGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IG1heWJlIHdlIGhhdmUgdG8gZW5kIHRoZSByZXF1ZXN0P1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiB0aGUgcmVxdWVzdCBoYXMgYSBwb3N0cG9uZWQgc3RhdGUgYW5kIGl0J3MgYSByZXN1bWUgcmVxdWVzdCB3ZVxuICAgICAgICAgICAgLy8gc2hvdWxkIGVycm9yLlxuICAgICAgICAgICAgaWYgKGRpZFBvc3Rwb25lICYmIG1pbmltYWxQb3N0cG9uZWQpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdJbnZhcmlhbnQ6IHBvc3Rwb25lZCBzdGF0ZSBzaG91bGQgbm90IGJlIHByZXNlbnQgb24gYSByZXN1bWUgcmVxdWVzdCcpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTM5NlwiLFxuICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoY2FjaGVkRGF0YS5oZWFkZXJzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaGVhZGVycyA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4uY2FjaGVkRGF0YS5oZWFkZXJzXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpZiAoIW1pbmltYWxNb2RlIHx8ICFpc1NTRykge1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgaGVhZGVyc1tORVhUX0NBQ0hFX1RBR1NfSEVBREVSXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKGhlYWRlcnMpKXtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ3VuZGVmaW5lZCcpIGNvbnRpbnVlO1xuICAgICAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgdiBvZiB2YWx1ZSl7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzLmFwcGVuZEhlYWRlcihrZXksIHYpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlID0gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5hcHBlbmRIZWFkZXIoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXMuYXBwZW5kSGVhZGVyKGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gQWRkIHRoZSBjYWNoZSB0YWdzIGhlYWRlciB0byB0aGUgcmVzcG9uc2UgaWYgaXQgZXhpc3RzIGFuZCB3ZSdyZSBpblxuICAgICAgICAgICAgLy8gbWluaW1hbCBtb2RlIHdoaWxlIHJlbmRlcmluZyBhIHN0YXRpYyBwYWdlLlxuICAgICAgICAgICAgY29uc3QgdGFncyA9IChfY2FjaGVkRGF0YV9oZWFkZXJzID0gY2FjaGVkRGF0YS5oZWFkZXJzKSA9PSBudWxsID8gdm9pZCAwIDogX2NhY2hlZERhdGFfaGVhZGVyc1tORVhUX0NBQ0hFX1RBR1NfSEVBREVSXTtcbiAgICAgICAgICAgIGlmIChtaW5pbWFsTW9kZSAmJiBpc1NTRyAmJiB0YWdzICYmIHR5cGVvZiB0YWdzID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoTkVYVF9DQUNIRV9UQUdTX0hFQURFUiwgdGFncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiB0aGUgcmVxdWVzdCBpcyBhIGRhdGEgcmVxdWVzdCwgdGhlbiB3ZSBzaG91bGRuJ3Qgc2V0IHRoZSBzdGF0dXMgY29kZVxuICAgICAgICAgICAgLy8gZnJvbSB0aGUgcmVzcG9uc2UgYmVjYXVzZSBpdCBzaG91bGQgYWx3YXlzIGJlIDIwMC4gVGhpcyBzaG91bGQgYmUgZ2F0ZWRcbiAgICAgICAgICAgIC8vIGJlaGluZCB0aGUgZXhwZXJpbWVudGFsIFBQUiBmbGFnLlxuICAgICAgICAgICAgaWYgKGNhY2hlZERhdGEuc3RhdHVzICYmICghaXNSU0NSZXF1ZXN0IHx8ICFpc1JvdXRlUFBSRW5hYmxlZCkpIHtcbiAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IGNhY2hlZERhdGEuc3RhdHVzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gUmVkaXJlY3QgaW5mb3JtYXRpb24gaXMgZW5jb2RlZCBpbiBSU0MgcGF5bG9hZCwgc28gd2UgZG9uJ3QgbmVlZCB0byB1c2UgcmVkaXJlY3Qgc3RhdHVzIGNvZGVzXG4gICAgICAgICAgICBpZiAoIW1pbmltYWxNb2RlICYmIGNhY2hlZERhdGEuc3RhdHVzICYmIFJlZGlyZWN0U3RhdHVzQ29kZVtjYWNoZWREYXRhLnN0YXR1c10gJiYgaXNSU0NSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgcmVzLnN0YXR1c0NvZGUgPSAyMDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBNYXJrIHRoYXQgdGhlIHJlcXVlc3QgZGlkIHBvc3Rwb25lLlxuICAgICAgICAgICAgaWYgKGRpZFBvc3Rwb25lKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIsICcxJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyB3ZSBkb24ndCBnbyB0aHJvdWdoIHRoaXMgYmxvY2sgd2hlbiBwcmV2aWV3IG1vZGUgaXMgdHJ1ZVxuICAgICAgICAgICAgLy8gYXMgcHJldmlldyBtb2RlIGlzIGEgZHluYW1pYyByZXF1ZXN0IChieXBhc3NlcyBjYWNoZSkgYW5kIGRvZXNuJ3RcbiAgICAgICAgICAgIC8vIGdlbmVyYXRlIGJvdGggSFRNTCBhbmQgcGF5bG9hZHMgaW4gdGhlIHNhbWUgcmVxdWVzdCBzbyBjb250aW51ZSB0byBqdXN0XG4gICAgICAgICAgICAvLyByZXR1cm4gdGhlIGdlbmVyYXRlZCBwYXlsb2FkXG4gICAgICAgICAgICBpZiAoaXNSU0NSZXF1ZXN0ICYmICFpc0RyYWZ0TW9kZSkge1xuICAgICAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSBkeW5hbWljIFJTQyByZXF1ZXN0LCB0aGVuIHN0cmVhbSB0aGUgcmVzcG9uc2UuXG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjYWNoZWREYXRhLnJzY0RhdGEgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjYWNoZWREYXRhLnBvc3Rwb25lZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignSW52YXJpYW50OiBFeHBlY3RlZCBwb3N0cG9uZWQgdG8gYmUgdW5kZWZpbmVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUzNzJcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzZW5kUmVuZGVyUmVzdWx0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdyc2MnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdDogY2FjaGVkRGF0YS5odG1sLFxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gRHluYW1pYyBSU0MgcmVzcG9uc2VzIGNhbm5vdCBiZSBjYWNoZWQsIGV2ZW4gaWYgdGhleSdyZVxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gY29uZmlndXJlZCB3aXRoIGBmb3JjZS1zdGF0aWNgIGJlY2F1c2Ugd2UgaGF2ZSBubyB3YXkgb2ZcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGRpc3Rpbmd1aXNoaW5nIGJldHdlZW4gYGZvcmNlLXN0YXRpY2AgYW5kIHBhZ2VzIHRoYXQgaGF2ZSBub1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gcG9zdHBvbmVkIHN0YXRlLlxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVE9ETzogZGlzdGluZ3Vpc2ggYGZvcmNlLXN0YXRpY2AgZnJvbSBwYWdlcyB3aXRoIG5vIHBvc3Rwb25lZCBzdGF0ZSAoc3RhdGljKVxuICAgICAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiBpc0R5bmFtaWNSU0NSZXF1ZXN0ID8ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gOiBjYWNoZUVudHJ5LmNhY2hlQ29udHJvbFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gQXMgdGhpcyBpc24ndCBhIHByZWZldGNoIHJlcXVlc3QsIHdlIHNob3VsZCBzZXJ2ZSB0aGUgc3RhdGljIGZsaWdodFxuICAgICAgICAgICAgICAgIC8vIGRhdGEuXG4gICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBSZW5kZXJSZXN1bHQuZnJvbVN0YXRpYyhjYWNoZWREYXRhLnJzY0RhdGEpLFxuICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBUaGlzIGlzIGEgcmVxdWVzdCBmb3IgSFRNTCBkYXRhLlxuICAgICAgICAgICAgbGV0IGJvZHkgPSBjYWNoZWREYXRhLmh0bWw7XG4gICAgICAgICAgICAvLyBJZiB0aGVyZSdzIG5vIHBvc3Rwb25lZCBzdGF0ZSwgd2Ugc2hvdWxkIGp1c3Qgc2VydmUgdGhlIEhUTUwuIFRoaXNcbiAgICAgICAgICAgIC8vIHNob3VsZCBhbHNvIGJlIHRoZSBjYXNlIGZvciBhIHJlc3VtZSByZXF1ZXN0IGJlY2F1c2UgaXQncyBjb21wbGV0ZWRcbiAgICAgICAgICAgIC8vIGFzIGEgc2VydmVyIHJlbmRlciAocmF0aGVyIHRoYW4gYSBzdGF0aWMgcmVuZGVyKS5cbiAgICAgICAgICAgIGlmICghZGlkUG9zdHBvbmUgfHwgbWluaW1hbE1vZGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAnaHRtbCcsXG4gICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgcG93ZXJlZEJ5SGVhZGVyOiBuZXh0Q29uZmlnLnBvd2VyZWRCeUhlYWRlcixcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBib2R5LFxuICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBJZiB3ZSdyZSBkZWJ1Z2dpbmcgdGhlIHN0YXRpYyBzaGVsbCBvciB0aGUgZHluYW1pYyBBUEkgYWNjZXNzZXMsIHdlXG4gICAgICAgICAgICAvLyBzaG91bGQganVzdCBzZXJ2ZSB0aGUgSFRNTCB3aXRob3V0IHJlc3VtaW5nIHRoZSByZW5kZXIuIFRoZSByZXR1cm5lZFxuICAgICAgICAgICAgLy8gSFRNTCB3aWxsIGJlIHRoZSBzdGF0aWMgc2hlbGwgc28gYWxsIHRoZSBEeW5hbWljIEFQSSdzIHdpbGwgYmUgdXNlZFxuICAgICAgICAgICAgLy8gZHVyaW5nIHN0YXRpYyBnZW5lcmF0aW9uLlxuICAgICAgICAgICAgaWYgKGlzRGVidWdTdGF0aWNTaGVsbCB8fCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzKSB7XG4gICAgICAgICAgICAgICAgLy8gU2luY2Ugd2UncmUgbm90IHJlc3VtaW5nIHRoZSByZW5kZXIsIHdlIG5lZWQgdG8gYXQgbGVhc3QgYWRkIHRoZVxuICAgICAgICAgICAgICAgIC8vIGNsb3NpbmcgYm9keSBhbmQgaHRtbCB0YWdzIHRvIGNyZWF0ZSB2YWxpZCBIVE1MLlxuICAgICAgICAgICAgICAgIGJvZHkuY2hhaW4obmV3IFJlYWRhYmxlU3RyZWFtKHtcbiAgICAgICAgICAgICAgICAgICAgc3RhcnQgKGNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShFTkNPREVEX1RBR1MuQ0xPU0VELkJPRFlfQU5EX0hUTUwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbGxlci5jbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgIHJldHVybiBzZW5kUmVuZGVyUmVzdWx0KHtcbiAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6ICdodG1sJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IGJvZHksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4cGlyZTogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFRoaXMgcmVxdWVzdCBoYXMgcG9zdHBvbmVkLCBzbyBsZXQncyBjcmVhdGUgYSBuZXcgdHJhbnNmb3JtZXIgdGhhdCB0aGVcbiAgICAgICAgICAgIC8vIGR5bmFtaWMgZGF0YSBjYW4gcGlwZSB0byB0aGF0IHdpbGwgYXR0YWNoIHRoZSBkeW5hbWljIGRhdGEgdG8gdGhlIGVuZFxuICAgICAgICAgICAgLy8gb2YgdGhlIHJlc3BvbnNlLlxuICAgICAgICAgICAgY29uc3QgdHJhbnNmb3JtZXIgPSBuZXcgVHJhbnNmb3JtU3RyZWFtKCk7XG4gICAgICAgICAgICBib2R5LmNoYWluKHRyYW5zZm9ybWVyLnJlYWRhYmxlKTtcbiAgICAgICAgICAgIC8vIFBlcmZvcm0gdGhlIHJlbmRlciBhZ2FpbiwgYnV0IHRoaXMgdGltZSwgcHJvdmlkZSB0aGUgcG9zdHBvbmVkIHN0YXRlLlxuICAgICAgICAgICAgLy8gV2UgZG9uJ3QgYXdhaXQgYmVjYXVzZSB3ZSB3YW50IHRoZSByZXN1bHQgdG8gc3RhcnQgc3RyZWFtaW5nIG5vdywgYW5kXG4gICAgICAgICAgICAvLyB3ZSd2ZSBhbHJlYWR5IGNoYWluZWQgdGhlIHRyYW5zZm9ybWVyJ3MgcmVhZGFibGUgdG8gdGhlIHJlbmRlciByZXN1bHQuXG4gICAgICAgICAgICBkb1JlbmRlcih7XG4gICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICBwb3N0cG9uZWQ6IGNhY2hlZERhdGEucG9zdHBvbmVkLFxuICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgYSByZXN1bWUgcmVuZGVyLCBub3QgYSBmYWxsYmFjayByZW5kZXIsIHNvIHdlIGRvbid0IG5lZWQgdG9cbiAgICAgICAgICAgICAgICAvLyBzZXQgdGhpcy5cbiAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zOiBudWxsXG4gICAgICAgICAgICB9KS50aGVuKGFzeW5jIChyZXN1bHQpPT57XG4gICAgICAgICAgICAgICAgdmFyIF9yZXN1bHRfdmFsdWU7XG4gICAgICAgICAgICAgICAgaWYgKCFyZXN1bHQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcignSW52YXJpYW50OiBleHBlY3RlZCBhIHJlc3VsdCB0byBiZSByZXR1cm5lZCcpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU0NjNcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoKChfcmVzdWx0X3ZhbHVlID0gcmVzdWx0LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX3Jlc3VsdF92YWx1ZS5raW5kKSAhPT0gQ2FjaGVkUm91dGVLaW5kLkFQUF9QQUdFKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhciBfcmVzdWx0X3ZhbHVlMTtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5ldyBFcnJvcihgSW52YXJpYW50OiBleHBlY3RlZCBhIHBhZ2UgcmVzcG9uc2UsIGdvdCAkeyhfcmVzdWx0X3ZhbHVlMSA9IHJlc3VsdC52YWx1ZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9yZXN1bHRfdmFsdWUxLmtpbmR9YCksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTMwNVwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFBpcGUgdGhlIHJlc3VtZSByZXN1bHQgdG8gdGhlIHRyYW5zZm9ybWVyLlxuICAgICAgICAgICAgICAgIGF3YWl0IHJlc3VsdC52YWx1ZS5odG1sLnBpcGVUbyh0cmFuc2Zvcm1lci53cml0YWJsZSk7XG4gICAgICAgICAgICB9KS5jYXRjaCgoZXJyKT0+e1xuICAgICAgICAgICAgICAgIC8vIEFuIGVycm9yIG9jY3VycmVkIGR1cmluZyBwaXBpbmcgb3IgcHJlcGFyaW5nIHRoZSByZW5kZXIsIGFib3J0XG4gICAgICAgICAgICAgICAgLy8gdGhlIHRyYW5zZm9ybWVycyB3cml0ZXIgc28gd2UgY2FuIHRlcm1pbmF0ZSB0aGUgc3RyZWFtLlxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybWVyLndyaXRhYmxlLmFib3J0KGVycikuY2F0Y2goKGUpPT57XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJjb3VsZG4ndCBhYm9ydCB0cmFuc2Zvcm1lclwiLCBlKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgdHlwZTogJ2h0bWwnLFxuICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgIHJlc3VsdDogYm9keSxcbiAgICAgICAgICAgICAgICAvLyBXZSBkb24ndCB3YW50IHRvIGNhY2hlIHRoZSByZXNwb25zZSBpZiBpdCBoYXMgcG9zdHBvbmVkIGRhdGEgYmVjYXVzZVxuICAgICAgICAgICAgICAgIC8vIHRoZSByZXNwb25zZSBiZWluZyBzZW50IHRvIHRoZSBjbGllbnQgaXQncyBkeW5hbWljIHBhcnRzIGFyZSBzdHJlYW1lZFxuICAgICAgICAgICAgICAgIC8vIHRvIHRoZSBjbGllbnQgb24gdGhlIHNhbWUgcmVxdWVzdC5cbiAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgLy8gVE9ETzogYWN0aXZlU3BhbiBjb2RlIHBhdGggaXMgZm9yIHdoZW4gd3JhcHBlZCBieVxuICAgICAgICAvLyBuZXh0LXNlcnZlciBjYW4gYmUgcmVtb3ZlZCB3aGVuIHRoaXMgaXMgbm8gbG9uZ2VyIHVzZWRcbiAgICAgICAgaWYgKGFjdGl2ZVNwYW4pIHtcbiAgICAgICAgICAgIGF3YWl0IGhhbmRsZVJlc3BvbnNlKGFjdGl2ZVNwYW4pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHRyYWNlci53aXRoUHJvcGFnYXRlZENvbnRleHQocmVxLmhlYWRlcnMsICgpPT50cmFjZXIudHJhY2UoQmFzZVNlcnZlclNwYW4uaGFuZGxlUmVxdWVzdCwge1xuICAgICAgICAgICAgICAgICAgICBzcGFuTmFtZTogYCR7bWV0aG9kfSAke3JlcS51cmx9YCxcbiAgICAgICAgICAgICAgICAgICAga2luZDogU3BhbktpbmQuU0VSVkVSLFxuICAgICAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC5tZXRob2QnOiBtZXRob2QsXG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC50YXJnZXQnOiByZXEudXJsXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LCBoYW5kbGVSZXNwb25zZSkpO1xuICAgICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIC8vIGlmIHdlIGFyZW4ndCB3cmFwcGVkIGJ5IGJhc2Utc2VydmVyIGhhbmRsZSBoZXJlXG4gICAgICAgIGlmICghYWN0aXZlU3Bhbikge1xuICAgICAgICAgICAgYXdhaXQgcm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnIsIHtcbiAgICAgICAgICAgICAgICByb3V0ZXJLaW5kOiAnQXBwIFJvdXRlcicsXG4gICAgICAgICAgICAgICAgcm91dGVQYXRoOiBzcmNQYWdlLFxuICAgICAgICAgICAgICAgIHJvdXRlVHlwZTogJ3JlbmRlcicsXG4gICAgICAgICAgICAgICAgcmV2YWxpZGF0ZVJlYXNvbjogZ2V0UmV2YWxpZGF0ZVJlYXNvbih7XG4gICAgICAgICAgICAgICAgICAgIGlzUmV2YWxpZGF0ZTogaXNTU0csXG4gICAgICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH0sIHJvdXRlclNlcnZlckNvbnRleHQpO1xuICAgICAgICB9XG4gICAgICAgIC8vIHJldGhyb3cgc28gdGhhdCB3ZSBjYW4gaGFuZGxlIHNlcnZpbmcgZXJyb3IgcGFnZVxuICAgICAgICB0aHJvdyBlcnI7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Chooks%5C%5Cuse-auth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Chooks%5C%5Cuse-auth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(rsc)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(rsc)/./components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(rsc)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./hooks/use-auth.tsx */ \"(rsc)/./hooks/use-auth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Chooks%5C%5Cuse-auth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProviders: () => (/* binding */ AppProviders),\n/* harmony export */   AppThemeProvider: () => (/* binding */ AppThemeProvider),\n/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryProvider,AppThemeProvider,AppProviders auto */ \n\n\n\n// Criar uma instância do QueryClient com configurações otimizadas\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 1000 * 60 * 5,\n            gcTime: 1000 * 60 * 10\n        }\n    }\n});\nfunction ReactQueryProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\providers.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\nfunction AppThemeProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"dark\",\n        enableSystem: false,\n        disableTransitionOnChange: true,\n        storageKey: \"aface-theme\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\providers.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\nfunction AppProviders({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQueryProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppThemeProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\providers.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\app\\\\providers.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* reexport safe */ sonner__WEBPACK_IMPORTED_MODULE_2__.toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _theme_wrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme-wrapper */ \"(ssr)/./components/ui/theme-wrapper.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme } = (0,_theme_wrapper__WEBPACK_IMPORTED_MODULE_1__.useSafeTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/theme-wrapper.tsx":
/*!*****************************************!*\
  !*** ./components/ui/theme-wrapper.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeWrapper: () => (/* binding */ ThemeWrapper),\n/* harmony export */   useSafeTheme: () => (/* binding */ useSafeTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeWrapper,useSafeTheme auto */ \n\n\n/**\r\n * Componente wrapper que evita problemas de hidratação relacionados ao tema\r\n * Garante que a aplicação do tema aconteça apenas no cliente após a hidratação\r\n */ function ThemeWrapper({ children }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeWrapper.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ThemeWrapper.useEffect\"], []);\n    // Garante que a aplicação do tema só aconteça após a hidratação\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\theme-wrapper.tsx\",\n            lineNumber: 24,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n/**\r\n * Hook para usar o tema de forma segura, evitando problemas de hidratação\r\n */ function useSafeTheme() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useSafeTheme.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"useSafeTheme.useEffect\"], []);\n    return {\n        theme: mounted ? theme : 'dark',\n        setTheme: mounted ? setTheme : ()=>{},\n        mounted\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/theme-wrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-auth.tsx":
/*!****************************!*\
  !*** ./hooks/use-auth.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Função para atualizar o usuário\n    const refreshUser = async ()=>{\n        try {\n            console.log('Atualizando informações do usuário...');\n            const currentUser = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n            setUser(currentUser);\n            console.log('Usuário atualizado:', currentUser?.email || 'nenhum usuário');\n        } catch (error) {\n            console.error('Erro ao buscar usuário:', error);\n            setUser(null);\n        }\n    };\n    // Função de logout\n    const signOut = async ()=>{\n        try {\n            console.log('Realizando logout...');\n            const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.authService.signOut();\n            if (result.success) {\n                setUser(null);\n                console.log('Logout realizado com sucesso');\n            } else {\n                console.error('Erro no logout:', result.error);\n            }\n        } catch (error) {\n            console.error('Erro no logout:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            console.log('Inicializando AuthProvider...');\n            // Verificar usuário atual na inicialização\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        await refreshUser();\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n            // Listener para mudanças de autenticação\n            const { data: { subscription } } = _lib_auth__WEBPACK_IMPORTED_MODULE_2__.authService.onAuthStateChange({\n                \"AuthProvider.useEffect\": (authUser)=>{\n                    console.log('Estado de autenticação alterado:', authUser?.email || 'logout');\n                    setUser(authUser);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    console.log('Limpando listener de autenticação');\n                    subscription?.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        user,\n        loading,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\aface-company\\\\hooks\\\\use-auth.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-auth.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./lib/supabase.ts\");\n\nclass AuthService {\n    getRedirectUrl() {\n        const baseUrl =  false ? 0 : \"https://instagram-analytics.loca.lt\" || 0;\n        return process.env.NEXT_PUBLIC_REDIRECT_URL || `${baseUrl}/auth/callback`;\n    }\n    // Cadastro de novo usuário\n    async signUp(data) {\n        try {\n            const { email, password, fullName } = data;\n            // Criar usuário no Supabase Auth\n            const { data: authData, error: authError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: fullName,\n                        display_name: fullName\n                    },\n                    emailRedirectTo: this.getRedirectUrl()\n                }\n            });\n            if (authError) {\n                return {\n                    success: false,\n                    error: this.translateError(authError.message)\n                };\n            }\n            if (!authData.user) {\n                return {\n                    success: false,\n                    error: 'Erro ao criar usuário'\n                };\n            }\n            return {\n                success: true,\n                user: {\n                    id: authData.user.id,\n                    email: authData.user.email,\n                    fullName: authData.user.user_metadata?.full_name,\n                    emailConfirmed: authData.user.email_confirmed_at ? true : false,\n                    createdAt: authData.user.created_at\n                },\n                message: authData.user.email_confirmed_at ? 'Conta criada com sucesso!' : 'Conta criada com sucesso! Verifique seu email para confirmar.'\n            };\n        } catch (error) {\n            console.error('Erro no signup:', error);\n            return {\n                success: false,\n                error: 'Erro interno do servidor'\n            };\n        }\n    }\n    // Login do usuário\n    async signIn(data) {\n        try {\n            const { email, password } = data;\n            const { data: authData, error: authError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (authError) {\n                return {\n                    success: false,\n                    error: this.translateError(authError.message)\n                };\n            }\n            if (!authData.user) {\n                return {\n                    success: false,\n                    error: 'Usuário não encontrado'\n                };\n            }\n            return {\n                success: true,\n                user: {\n                    id: authData.user.id,\n                    email: authData.user.email,\n                    fullName: authData.user.user_metadata?.full_name,\n                    emailConfirmed: authData.user.email_confirmed_at ? true : false,\n                    createdAt: authData.user.created_at\n                },\n                message: 'Login realizado com sucesso!'\n            };\n        } catch (error) {\n            console.error('Erro no signin:', error);\n            return {\n                success: false,\n                error: 'Erro interno do servidor'\n            };\n        }\n    }\n    // Logout\n    async signOut() {\n        try {\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n            if (error) {\n                return {\n                    success: false,\n                    error: this.translateError(error.message)\n                };\n            }\n            return {\n                success: true,\n                message: 'Logout realizado com sucesso!'\n            };\n        } catch (error) {\n            console.error('Erro no signout:', error);\n            return {\n                success: false,\n                error: 'Erro interno do servidor'\n            };\n        }\n    }\n    // Login com Google\n    async signInWithGoogle() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: this.getRedirectUrl(),\n                    queryParams: {\n                        access_type: 'offline',\n                        prompt: 'consent'\n                    }\n                }\n            });\n            if (error) {\n                return {\n                    success: false,\n                    error: this.translateError(error.message)\n                };\n            }\n            return {\n                success: true,\n                message: 'Redirecionando para o Google...'\n            };\n        } catch (error) {\n            console.error('Erro no Google OAuth:', error);\n            return {\n                success: false,\n                error: 'Erro interno do servidor'\n            };\n        }\n    }\n    // Login com GitHub\n    async signInWithGitHub() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithOAuth({\n                provider: 'github',\n                options: {\n                    redirectTo: this.getRedirectUrl()\n                }\n            });\n            if (error) {\n                return {\n                    success: false,\n                    error: this.translateError(error.message)\n                };\n            }\n            return {\n                success: true,\n                message: 'Redirecionando para o GitHub...'\n            };\n        } catch (error) {\n            console.error('Erro no GitHub OAuth:', error);\n            return {\n                success: false,\n                error: 'Erro interno do servidor'\n            };\n        }\n    }\n    // Obter usuário atual\n    async getCurrentUser() {\n        try {\n            const { data: { user }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (error || !user) {\n                console.log('Nenhum usuário autenticado ou erro:', error?.message);\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                fullName: user.user_metadata?.full_name || user.user_metadata?.display_name,\n                emailConfirmed: user.email_confirmed_at ? true : false,\n                createdAt: user.created_at\n            };\n        } catch (error) {\n            console.error('Erro ao obter usuário atual:', error);\n            return null;\n        }\n    }\n    // Reenviar email de confirmação\n    async resendConfirmation(email) {\n        try {\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n                type: 'signup',\n                email,\n                options: {\n                    emailRedirectTo: this.getRedirectUrl()\n                }\n            });\n            if (error) {\n                return {\n                    success: false,\n                    error: this.translateError(error.message)\n                };\n            }\n            return {\n                success: true,\n                message: 'Email de confirmação reenviado!'\n            };\n        } catch (error) {\n            console.error('Erro ao reenviar confirmação:', error);\n            return {\n                success: false,\n                error: 'Erro interno do servidor'\n            };\n        }\n    }\n    // Redefinir senha\n    async resetPassword(email) {\n        try {\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/reset-password`\n            });\n            if (error) {\n                return {\n                    success: false,\n                    error: this.translateError(error.message)\n                };\n            }\n            return {\n                success: true,\n                message: 'Email de redefinição de senha enviado!'\n            };\n        } catch (error) {\n            console.error('Erro ao redefinir senha:', error);\n            return {\n                success: false,\n                error: 'Erro interno do servidor'\n            };\n        }\n    }\n    // Listener para mudanças de autenticação\n    onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log('Auth state changed:', event, session?.user?.email);\n            if (session?.user) {\n                const user = {\n                    id: session.user.id,\n                    email: session.user.email,\n                    fullName: session.user.user_metadata?.full_name || session.user.user_metadata?.display_name,\n                    emailConfirmed: session.user.email_confirmed_at ? true : false,\n                    createdAt: session.user.created_at\n                };\n                callback(user);\n            } else {\n                callback(null);\n            }\n        });\n    }\n    // Traduzir erros do Supabase para português\n    translateError(error) {\n        const translations = {\n            'Invalid login credentials': 'Email ou senha incorretos',\n            'Email not confirmed': 'Email não confirmado. Verifique sua caixa de entrada.',\n            'User already registered': 'Este email já está registrado',\n            'Password should be at least 6 characters': 'A senha deve ter pelo menos 6 caracteres',\n            'Invalid email': 'Email inválido',\n            'Signup is disabled': 'Cadastro está desabilitado',\n            'Email rate limit exceeded': 'Muitas tentativas. Tente novamente mais tarde.',\n            'Invalid refresh token': 'Sessão expirada. Faça login novamente.',\n            'User not found': 'Usuário não encontrado',\n            'Session not found': 'Sessão não encontrada',\n            'Token has expired or is invalid': 'Token expirado ou inválido',\n            'To signup, please provide your email': 'Para cadastrar-se, forneça seu email',\n            'Unable to validate email address: invalid format': 'Formato de email inválido',\n            'Password should be at least 6 characters.': 'A senha deve ter pelo menos 6 caracteres'\n        };\n        return translations[error] || error;\n    }\n}\nconst authService = new AuthService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\nconst supabaseUrl = \"https://eccxyciznmjzsgchlhyr.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVjY3h5Y2l6bm1qenNnY2hsaHlyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5MDE1OTYsImV4cCI6MjA2ODQ3NzU5Nn0.LfAARu3CUDDvKlOWORXiBNz0fF26d2XR8AgseBf8ZHY\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\n// Cliente para o frontend (usa anon key) com configuração PKCE\nconst supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        flowType: 'pkce',\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Cliente para operações do servidor (apenas se SERVICE_KEY estiver disponível)\nconst supabaseAdmin = process.env.SUPABASE_SERVICE_KEY ? (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, process.env.SUPABASE_SERVICE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false,\n        detectSessionInUrl: false\n    }\n}) : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERvY3VtZW50c1xcYWZhY2UtY29tcGFueVxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Chooks%5C%5Cuse-auth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Chooks%5C%5Cuse-auth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./hooks/use-auth.tsx */ \"(ssr)/./hooks/use-auth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluaXN0cmF0b3IlNUMlNUNEb2N1bWVudHMlNUMlNUNhZmFjZS1jb21wYW55JTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWRtaW5pc3RyYXRvciU1QyU1Q0RvY3VtZW50cyU1QyU1Q2FmYWNlLWNvbXBhbnklNUMlNUNhcHAlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXBwUHJvdmlkZXJzJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluaXN0cmF0b3IlNUMlNUNEb2N1bWVudHMlNUMlNUNhZmFjZS1jb21wYW55JTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDc29ubmVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWRtaW5pc3RyYXRvciU1QyU1Q0RvY3VtZW50cyU1QyU1Q2FmYWNlLWNvbXBhbnklNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUN0b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWRtaW5pc3RyYXRvciU1QyU1Q0RvY3VtZW50cyU1QyU1Q2FmYWNlLWNvbXBhbnklNUMlNUNob29rcyU1QyU1Q3VzZS1hdXRoLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQTZJO0FBQzdJO0FBQ0EsZ0tBQWdKO0FBQ2hKO0FBQ0Esa0tBQWlKO0FBQ2pKO0FBQ0Esb0pBQThJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBcHBQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbmlzdHJhdG9yXFxcXERvY3VtZW50c1xcXFxhZmFjZS1jb21wYW55XFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUb2FzdGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5pc3RyYXRvclxcXFxEb2N1bWVudHNcXFxcYWZhY2UtY29tcGFueVxcXFxjb21wb25lbnRzXFxcXHVpXFxcXHNvbm5lci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbmlzdHJhdG9yXFxcXERvY3VtZW50c1xcXFxhZmFjZS1jb21wYW55XFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcdG9hc3Rlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEFkbWluaXN0cmF0b3JcXFxcRG9jdW1lbnRzXFxcXGFmYWNlLWNvbXBhbnlcXFxcaG9va3NcXFxcdXNlLWF1dGgudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Chooks%5C%5Cuse-auth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDocuments%5C%5Caface-company%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/@tanstack","vendor-chunks/sonner","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/lucide-react","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caface-company&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();