import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { instagramAPI } from '@/lib/instagram-api';

/**
 * Rota para iniciar o processo de autenticação com Instagram
 * GET /auth/instagram/login (correspondente ao analytics-connect)
 */
export async function GET(request: NextRequest) {
  try {
    // Gera um estado único para validação de segurança
    const state = crypto.randomBytes(32).toString('hex');

    // Define as permissões necessárias (Instagram API with Instagram Login)
    const scopes = [
      'instagram_business_basic',           // Acesso básico ao perfil profissional
      'instagram_business_content_publish', // Publicar conteúdo
      'instagram_business_manage_messages', // Gerenciar mensagens
      'instagram_business_manage_comments', // Gerenciar comentários
      'instagram_business_manage_insights'  // Insights e analytics (ESSENCIAL!)
    ];

    // Gera a URL de autorização
    const authUrl = instagramAPI.getAuthorizationUrl(scopes, state);

    console.log('🚀 Iniciando autenticação Instagram');
    console.log('🔗 URL de redirecionamento:', authUrl);

    // Resposta JSON com a URL para redirecionamento
    return NextResponse.json({
      success: true,
      message: 'URL de autenticação gerada com sucesso',
      authUrl: authUrl,
      state: state,
      scopes: scopes,
      instructions: {
        step1: 'Acesse a URL fornecida',
        step2: 'Faça login DIRETAMENTE na sua conta Instagram Business/Creator',
        step3: 'Autorize as permissões solicitadas',
        step4: 'Você será redirecionado de volta com os dados de acesso'
      }
    });

  } catch (error: any) {
    console.error('❌ Erro ao gerar URL de autenticação:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    }, { status: 500 });
  }
} 