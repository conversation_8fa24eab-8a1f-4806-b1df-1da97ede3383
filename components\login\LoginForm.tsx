'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { authService } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import type { SignInFormData } from '@/types/auth';

interface FormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

export const LoginForm: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
    rememberMe: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = 'E-mail é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'E-mail deve ter um formato válido';
    }

    if (!formData.password) {
      newErrors.password = 'Senha é obrigatória';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Senha deve ter pelo menos 6 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Erro na validação",
        description: "Por favor, corrija os erros no formulário antes de continuar.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const loginData: SignInFormData = {
        email: formData.email.trim(),
        password: formData.password,
        rememberMe: formData.rememberMe,
      };

      const result = await authService.signIn(loginData);
      
      if (result.success) {
        setLoginSuccess(true);
        toast({
          title: "Login realizado com sucesso!",
          description: result.message,
          variant: "default",
        });
        
        // Redirecionar para dashboard após 1 segundo
        setTimeout(() => {
          router.push('/dashboard');
        }, 1000);
      } else {
        setErrors({ general: result.error || 'Erro no login' });
        toast({
          title: "Erro no login",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Erro no login:', error);
      setErrors({ general: 'Erro inesperado. Tente novamente.' });
      toast({
        title: "Erro inesperado",
        description: "Ocorreu um erro durante o login. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormBasicallyValid = () => {
    return formData.email.trim() && formData.password.length >= 6;
  };

  // Se o login foi bem-sucedido, mostrar mensagem de sucesso
  if (loginSuccess) {
    return (
      <div className="w-full  mx-auto space-y-6">
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Login realizado com sucesso!</strong>
            <br />
            Redirecionando para o dashboard...
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="w-full  mx-auto space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Erro geral */}
        {errors.general && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {errors.general}
            </AlertDescription>
          </Alert>
        )}

        {/* Campo E-mail */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium text-foreground">
            E-mail
          </Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="Digite seu e-mail"
            className={cn(
              "h-11 transition-colors",
              errors.email && "border-destructive focus-visible:ring-destructive"
            )}
          />
          {errors.email && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{errors.email}</span>
            </div>
          )}
        </div>

        {/* Campo Senha */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="text-sm font-medium text-foreground">
              Senha
            </Label>
            <Link 
              href="/forgot-password" 
              className="text-sm text-[#fa12e3] hover:underline"
            >
              Esqueceu a senha?
            </Link>
          </div>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="Digite sua senha"
              className={cn(
                "h-11 pr-10 transition-colors",
                errors.password && "border-destructive focus-visible:ring-destructive"
              )}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label={showPassword ? 'Ocultar senha' : 'Mostrar senha'}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>

          {errors.password && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{errors.password}</span>
            </div>
          )}
        </div>

        {/* Checkbox Lembrar */}
        <div className="flex items-center gap-3">
          <input
            id="rememberMe"
            type="checkbox"
            checked={formData.rememberMe}
            onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
          />
          <Label htmlFor="rememberMe" className="text-sm text-muted-foreground cursor-pointer">
            Lembrar de mim
          </Label>
        </div>

        {/* Botão de envio */}
        <Button
          type="submit"
          disabled={!isFormBasicallyValid() || isSubmitting}
          className={cn(
            "w-full h-11 text-base font-semibold transition-all duration-200",
            "bg-[#fa12e3] hover:bg-[#E63E38] active:scale-[0.98]",
            "disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed"
          )}
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              Entrando...
            </div>
          ) : (
            'Entrar'
          )}
        </Button>

        {/* Link para cadastro */}
        <div className="text-center text-sm text-muted-foreground">
          Ainda não tem uma conta?{' '}
          <Link href="/sign-up" className="text-[#fa12e3] font-medium hover:underline">
            Criar conta
          </Link>
        </div>
      </form>
    </div>
  );
}; 